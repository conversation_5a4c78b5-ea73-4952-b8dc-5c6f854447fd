as7058_raw_app.c:191:16:check_agc_statuses_change	40	static
as7058_raw_app.c:260:16:check_status_events_change	40	static
as7058_raw_app.c:288:17:size_of_packet	24	static
as7058_raw_app.c:312:17:remaining_size_in_packet	24	static
as7058_raw_app.c:334:19:create_packet_if_needed	16	static
as7058_raw_app.c:373:19:handle_agc_statuses_change	16	static
as7058_raw_app.c:409:19:handle_status_events_change	40	static
as7058_raw_app.c:444:19:handle_ext_event_cnt	24	static
as7058_raw_app.c:480:19:handle_fifo_data	48	static
as7058_raw_app.c:525:19:handle_acc_data	40	static
as7058_raw_app.c:570:19:copy_acc_data	16	static
as7058_raw_app.c:596:19:copy_agc_data	16	static
as7058_raw_app.c:624:19:finalize_packet	16	static
as7058_raw_app.c:657:19:get_packet_from_output_queue	32	static
as7058_raw_app.c:700:16:count_bits	24	static
as7058_raw_app.c:710:19:calculate_average_sample_period_us	80	static
as7058_raw_app.c:772:19:set_sample_period	48	static
as7058_raw_app.c:804:12:as7058_raw_app_initialize	16	static
as7058_raw_app.c:820:12:as7058_raw_app_configure	24	static
as7058_raw_app.c:838:12:as7058_raw_app_start	32	static
as7058_raw_app.c:858:12:as7058_raw_app_set_input	48	static
as7058_raw_app.c:957:12:as7058_raw_app_execute	16	static
as7058_raw_app.c:972:12:as7058_raw_app_get_output	16	static
as7058_raw_app.c:982:12:as7058_raw_app_stop	4	static
as7058_raw_app.c:993:12:as7058_raw_app_shutdown	4	static
