/*
 * AWT_SENSORS_DATA.c
 *
 *  Created on: 23-Oct-2021
 *      Author: Du<PERSON>h_Mali
 */



#include "event_handler.h"
#include "AWT_SENSORS_DATA.h"
#include "database.h"
#include "watch_state.h"
#include "event_groups.h"
#include "AWT_parameterRequest.h"
#include "Awt_Pmic.h"
#include "Wireless.h"
#include "AWT_UI_Wireless.h"
#include "UpperLayerInterface.h"
#include "OptimizedTransfer.h"
#include "Awt_lsm6dsox.h"

#if AWT_SENSOR_DATA_DEBUG
#define DEBUG_PRINTS
#else
#undef DEBUG_PRINTS
#endif

uint32_t SensorQueueError =0;

uint32_t _GetSOSMode();

uint32_t d_ecg_c = 0;
uint32_t d_spo2_c = 0;
uint32_t n_ecg_c = 0;


#define DR_DATA_HARDCODED_UI 0 //DebugRearchData_..

#if DR_DATA_HARDCODED_UI
extern float HARDCODED_ECG[2000];
#endif

extern xQueueHandle GUI_Queue;
extern QueueHandle_t SENSOR_Queue;

extern QueueHandle_t WiFi_data_queue;
extern QueueHandle_t POPUP_Queue;
extern QueueHandle_t HWKeyQueue;
// event group for tasks
extern EventGroupHandle_t  xSensorTskEvnt ;
extern TaskHandle_t Periodic_Measure		;
extern uint32_t AWT_errno					;
extern uint16_t HHcount        ;

typedef struct{
	uint32_t *rawPpgGreen_HRPtr;
	uint32_t *rawPpgRed_SPO2Ptr;
	uint32_t *rawPpgIr_SPO2Ptr;
	uint32_t *rawPpgAmb_SPO2Ptr;
	uint32_t *rawEcg_BpPtr;
	uint32_t *rawPpgGreen_BpPtr;
	uint32_t *rawEcg_ECGPtr;
	uint32_t *algo_Output;
	uint8_t *OcareRawDataptr;
}RAW_SIGNALS;

//TODO This Structure needs to be go in Database.h
typedef struct{
	HR_STRUCT			HEART_RATE;
	SPO2_STRUCT 		SPO2;
	BP_STRUCT 			BP;
	ECG_STRUCT 			ECG;
	TEMPERATURE_STRUCT 	TEMPERATURE;
	GSR_STRUCT			GSR;
	BATTERY_STRUCT 		BAT;
	PEDO_STRUCT 		PEDOMETER;
	RAW_SIGNALS			RAW_SIGNALS_VITALS;
}SENSOR_VITAL_DATA;


static SENSOR_DATA_STRUCT DataBaseCtx;


SENSOR_VITAL_DATA  __attribute__((section (".usr_config_area")))  GLOBAL_DATA;


static void Check_Vital_Range(SENSOR_DATA_STRUCT *VITAL_DATA);

void Reset_Database_Ctx()
{
	DataBaseCtx.REQUEST_ID = ReqIDNONE;
	DataBaseCtx.VITAL_REQUESTED_PARAM = VITAL_NONE;
	DataBaseCtx.UNIX_TIME =0;
	DataBaseCtx.listener = 0;
	memset(&DataBaseCtx.vitals,0,sizeof(DataBaseCtx.vitals));
}

static uint32_t DataBaseReset()
{
	uint32_t c_time =  Get_Current_Unix_Time();

	if((c_time >= READ_PERSISTENT_DATA(ResetTime)) )
	{

		c_time = Get_Time_Till_Midnight();
		COPY_PERSISTENT_DATA(ResetTime,c_time);
		PedometerReset();

		GLOBAL_DATA.HEART_RATE.hrmValue=0;
		GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE =0;
		GLOBAL_DATA.SPO2.spo2value=0;
		GLOBAL_DATA.SPO2.TIME_SPO2=0;
		GLOBAL_DATA.TEMPERATURE.cbt=0;
		GLOBAL_DATA.TEMPERATURE.time =0;
		GLOBAL_DATA.BP.systolic=0;
		GLOBAL_DATA.BP.diastolic=0;
		GLOBAL_DATA.BP.TIME_BLOOD_PRESSURE=0;
		GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr=0;
		GLOBAL_DATA.ECG.TIME_ECG=0;;
		GLOBAL_DATA.GSR.gsr=0;
		GLOBAL_DATA.GSR.TIME_GSR=0;
		GLOBAL_DATA.BP.PQRST_parameters.ECG_hr=0;
		memset(&GLOBAL_DATA.ECG.PQRST_parameters,0,sizeof(GLOBAL_DATA.ECG.PQRST_parameters));
		memset(&GLOBAL_DATA.HEART_RATE.HRM_HISTORY,0,sizeof(GLOBAL_DATA.HEART_RATE.HRM_HISTORY));
		memset(&GLOBAL_DATA.SPO2.SPO2_HISTORY,0,sizeof(GLOBAL_DATA.SPO2.SPO2_HISTORY));
		memset(&GLOBAL_DATA.BP.TIME_BP,0,sizeof(GLOBAL_DATA.BP.TIME_BP));
		memset(&GLOBAL_DATA.TEMPERATURE.CBT_HISTORY,0,sizeof(GLOBAL_DATA.TEMPERATURE.CBT_HISTORY));
		return 1;
	}

	return 0;

}

static void Retrive_Global_Database()
{
	memcpy(GLOBAL_DATA.HEART_RATE.HRM_HISTORY, &(READ_PERSISTENT_DATA(HRM_HISTORY)),sizeof(GLOBAL_DATA.HEART_RATE.HRM_HISTORY));
	memcpy(GLOBAL_DATA.SPO2.SPO2_HISTORY, &(READ_PERSISTENT_DATA(SPO2_HISTORY)),sizeof(GLOBAL_DATA.SPO2.SPO2_HISTORY));
	memcpy(GLOBAL_DATA.BP.BP_HISTORY, &(READ_PERSISTENT_DATA(BP_HISTORY)),sizeof(GLOBAL_DATA.BP.BP_HISTORY));
	memcpy(GLOBAL_DATA.BP.TIME_BP, &(READ_PERSISTENT_DATA(TIME_BP)),sizeof(GLOBAL_DATA.BP.TIME_BP));
	memcpy(&GLOBAL_DATA.PEDOMETER, &(READ_PERSISTENT_DATA(pedometer)),sizeof(GLOBAL_DATA.PEDOMETER));
	GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE = READ_PERSISTENT_DATA(TIME_HEART_RATE);
	GLOBAL_DATA.SPO2.TIME_SPO2 = READ_PERSISTENT_DATA(TIME_SPO2);

	previousValuesFromHistory();
}

static void Save_Global_Database()
{
	COPY_PERSISTENT_DATA(HRM_HISTORY,GLOBAL_DATA.HEART_RATE.HRM_HISTORY);
	COPY_PERSISTENT_DATA(SPO2_HISTORY,GLOBAL_DATA.SPO2.SPO2_HISTORY);
	COPY_PERSISTENT_DATA(BP_HISTORY,GLOBAL_DATA.BP.BP_HISTORY);
	COPY_PERSISTENT_DATA(TIME_BP,GLOBAL_DATA.BP.TIME_BP);
	COPY_PERSISTENT_DATA(pedometer,GLOBAL_DATA.PEDOMETER);
	COPY_PERSISTENT_DATA(TIME_HEART_RATE,GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE);
	COPY_PERSISTENT_DATA(TIME_SPO2,GLOBAL_DATA.SPO2.TIME_SPO2);
}

void previousValuesFromHistory()
{
	for (uint8_t index = HHcount; index > 0; index--)
	{
		if (GLOBAL_DATA.HEART_RATE.HRM_HISTORY[index] >= 30 && GLOBAL_DATA.HEART_RATE.HRM_HISTORY[index] <= 230)
		{
			GLOBAL_DATA.HEART_RATE.hrmValue = GLOBAL_DATA.HEART_RATE.HRM_HISTORY[index];
			break;
		}
	}

	for (uint8_t index = HHcount; index > 0; index--)
	{
		if (GLOBAL_DATA.SPO2.SPO2_HISTORY[index] >= 60 && GLOBAL_DATA.SPO2.SPO2_HISTORY[index] <= 100)
		{
			GLOBAL_DATA.SPO2.spo2value = GLOBAL_DATA.SPO2.SPO2_HISTORY[index];
			break;
		}
	}

	GLOBAL_DATA.BP.systolic = GLOBAL_DATA.BP.BP_HISTORY[0];
	GLOBAL_DATA.BP.diastolic = GLOBAL_DATA.BP.BP_HISTORY[1];
}


static inline void Save_Startup_Time()
{
	uint32_t time = Get_Current_Unix_Time();
	COPY_PERSISTENT_DATA(Switchon_Time,time);
}

void Init_Quent_Storage(enum awt_watch_state state)
{
	switch(state)
	{
	case PWR_ON:
	case RESTART:
	case SW_RST:
	case HW_RST:
		INIT_USER_DETAIL();
		INIT_PERSISTENT_DATA();
		Save_Startup_Time();
		if(!DataBaseReset())
			Retrive_Global_Database();
		break;

	case SLEEP_WKE:
		DataBaseReset();
		break;

	case CHRG:
		break;

	default :
		break;
	}

}

void Save_Quent_Storage()
{
	Save_Global_Database();
	FLASH_USER_DETAIL();
	FLASH_PERSISTENT_DATA();
}


REQUESTED_ID Get_Data_Requester()
{
	return DataBaseCtx.REQUEST_ID;
}

void Set_Data_Requester(REQUESTED_ID ID)
{
	DataBaseCtx.REQUEST_ID = ID;
}

void Set_Data_Listener(REQUESTED_ID _listener)
{
	DataBaseCtx.listener = _listener;
}

VITAL_PARAMETER_ID Get_Data_Parameter()
{
	return DataBaseCtx.VITAL_REQUESTED_PARAM;
}
void Set_Data_Parameter(VITAL_PARAMETER_ID vital)
{
	DataBaseCtx.VITAL_REQUESTED_PARAM = vital;
}

void Set_TimeStamp()
{
	DataBaseCtx.UNIX_TIME = Get_Current_Unix_Time();
}

uint32_t Get_TimeStamp()
{
	return DataBaseCtx.UNIX_TIME;
}

void updateHeartRate(uint8_t Heart)
{
	//GLOBAL_DATA.HEART_RATE.hrmValue = Heart;
	if(Heart > 30 && Heart <= 230)
		GLOBAL_DATA.HEART_RATE.hrmValue = Heart;
	//GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE = Get_Current_Unix_Time();
	//GLOBAL_DATA.HEART_RATE.HRM_HISTORY[HHcount] = GLOBAL_DATA.HEART_RATE.hrmValue;
}

void updateSpo2Rate(uint8_t SPO2)
{
	if(SPO2 > 60 && SPO2 <= 100)
		GLOBAL_DATA.SPO2.spo2value = SPO2;
	//GLOBAL_DATA.SPO2.TIME_SPO2 = Get_Current_Unix_Time();
	//GLOBAL_DATA.SPO2.SPO2_HISTORY[HHcount] = GLOBAL_DATA.SPO2.spo2value;
}

void updateCbt(float temp, uint32_t duration)
{
	GLOBAL_DATA.TEMPERATURE.cbt = temp;
	GLOBAL_DATA.TEMPERATURE.time = duration;
}

#if RAW_DATA
void UPDATE_DATABASE_WITH_RAWDATA(SENSOR_DATA_STRUCT *VITAL_DATA)
{
	switch(VITAL_DATA->VITAL_REQUESTED_PARAM)
	{
	case Heart_Rate:
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_HRPtr = VITAL_DATA->vitals.HRM.rawPpgGreenPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.algo_Output = VITAL_DATA->vitals.HRM.algo_Output;

		break;

	case SpO2:
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgRed_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgRedPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgIr_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgIrPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgAmb_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgAmbPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.algo_Output = VITAL_DATA->vitals.SPO2.algoOutPtr;

		break;

	case BP:
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_BpPtr = VITAL_DATA->vitals.BP.rawppgGreenPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_BpPtr = VITAL_DATA->vitals.BP.rawEcgPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.algo_Output = VITAL_DATA->vitals.BP.algo_outPtr;

		break;

	case ECG:
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_ECGPtr = VITAL_DATA->vitals.ECG.rawEcgPtr;
		break;

	default:
		break;
	}
}
#endif

void UPDATE_DATABASE_WITH_TIME(SENSOR_DATA_STRUCT *VITAL_DATA)
{
	switch(VITAL_DATA->VITAL_REQUESTED_PARAM)
	{
	case Heart_Rate:
#if DR_DATA_HARDCODED_UI
		GLOBAL_DATA.HEART_RATE.hrmValue = 72;
		GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE = 1640350265; //18:21:00/24-DEC-2021-FRIDAY
#else
		GLOBAL_DATA.HEART_RATE.hrmValue = VITAL_DATA->vitals.HRM.hrmValue;
		GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE = VITAL_DATA->UNIX_TIME;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_HRPtr = VITAL_DATA->vitals.HRM.rawPpgGreenPtr;



		GLOBAL_DATA.HEART_RATE.HRM_HISTORY[HHcount]=GLOBAL_DATA.HEART_RATE.hrmValue;

#endif
		break;

	case SpO2:
#ifdef DR_DATA_HARDCODED_UI
		GLOBAL_DATA.SPO2.spo2value = 98;
		GLOBAL_DATA.SPO2.TIME_SPO2 = Get_Current_Unix_Time();
#else
		GLOBAL_DATA.SPO2.spo2value = VITAL_DATA->vitals.SPO2.spo2value;
		GLOBAL_DATA.SPO2.TIME_SPO2 = VITAL_DATA->UNIX_TIME;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgRed_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgRedPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgIr_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgIrPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgAmb_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgAmbPtr;
#endif
		GLOBAL_DATA.SPO2.SPO2_HISTORY[HHcount] = GLOBAL_DATA.SPO2.spo2value;
		break;

	case Body_Temperature:
#if DR_DATA_HARDCODED_UI
		GLOBAL_DATA.TEMPERATURE.cbt = 65.30;
		GLOBAL_DATA.TEMPERATURE.time =  1640350265; //18:21:00/24-DEC-2021-FRIDAY
#else
		GLOBAL_DATA.TEMPERATURE.cbt = VITAL_DATA->vitals.TEMP;
		GLOBAL_DATA.TEMPERATURE.time = VITAL_DATA->UNIX_TIME;

		GLOBAL_DATA.TEMPERATURE.CBT_HISTORY[HHcount] = GLOBAL_DATA.TEMPERATURE.cbt;
#endif
		break;

	case BP:
#ifdef DR_DATA_HARDCODED_UI
		GLOBAL_DATA.BP.diastolic = 80;
		GLOBAL_DATA.BP.systolic = 120;
		GLOBAL_DATA.BP.PQRST_parameters.ECG_hr = 80;
		GLOBAL_DATA.BP.TIME_BLOOD_PRESSURE = Get_Current_Unix_Time();
#else
		GLOBAL_DATA.BP.systolic = VITAL_DATA->vitals.BP.systolic;
		GLOBAL_DATA.BP.diastolic = VITAL_DATA->vitals.BP.diastolic;
		GLOBAL_DATA.BP.PQRST_parameters.ECG_hr = VITAL_DATA->vitals.BP.PQRST_parameters.ECG_hr;
		GLOBAL_DATA.BP.TIME_BLOOD_PRESSURE = VITAL_DATA->UNIX_TIME;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_BpPtr = VITAL_DATA->vitals.BP.rawppgGreenPtr;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_BpPtr = VITAL_DATA->vitals.BP.rawEcgPtr;
#endif

		for(int i=23;i>2;)
		{
			GLOBAL_DATA.BP.BP_HISTORY[i]=GLOBAL_DATA.BP.BP_HISTORY[i-2];
			GLOBAL_DATA.BP.BP_HISTORY[i-1] = GLOBAL_DATA.BP.BP_HISTORY[i-3];
			i=i-2;
		}
		for(int j = 11; j>0;)
		{
			GLOBAL_DATA.BP.TIME_BP[j] = GLOBAL_DATA.BP.TIME_BP[j-1];
			j--;
		}
		GLOBAL_DATA.BP.BP_HISTORY[0]=GLOBAL_DATA.BP.systolic;
		GLOBAL_DATA.BP.BP_HISTORY[1]=GLOBAL_DATA.BP.diastolic;
		GLOBAL_DATA.BP.TIME_BP[0] =GLOBAL_DATA.BP.TIME_BLOOD_PRESSURE;

		break;

	case ECG:
#if DR_DATA_HARDCODED_UI
		GLOBAL_DATA.ECG.ecgPtr = HARDCODED_ECG;
		GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr = 88;
		GLOBAL_DATA.ECG.TIME_ECG = 1640350265; //18:21:00/24-DEC-2021-FRIDAY
#else
		GLOBAL_DATA.ECG.ecgPtr = VITAL_DATA->vitals.ECG.ecgPtr;
		GLOBAL_DATA.ECG.filterEcPtr = VITAL_DATA->vitals.ECG.filterEcPtr;
		GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr = VITAL_DATA->vitals.ECG.PQRST_parameters.ECG_hr;
		GLOBAL_DATA.ECG.PQRST_parameters.pr = VITAL_DATA->vitals.ECG.PQRST_parameters.pr;
		GLOBAL_DATA.ECG.PQRST_parameters.qrs = VITAL_DATA->vitals.ECG.PQRST_parameters.qrs;
		GLOBAL_DATA.ECG.PQRST_parameters.rr = VITAL_DATA->vitals.ECG.PQRST_parameters.rr;
		GLOBAL_DATA.ECG.PQRST_parameters.qt = VITAL_DATA->vitals.ECG.PQRST_parameters.qt;
		GLOBAL_DATA.ECG.PQRST_parameters.Inferences = VITAL_DATA->vitals.ECG.PQRST_parameters.Inferences;

		GLOBAL_DATA.ECG.TIME_ECG = VITAL_DATA->UNIX_TIME;
		GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_ECGPtr = VITAL_DATA->vitals.ECG.rawEcgPtr;
#endif
		break;

	default:
		break;
	}
}

uint32_t NOTIFY_VITAL_PARAMETER(SENSOR_DATA_STRUCT *VITAL_DATA)
{
#ifdef DEBUG_PRINTS
	printf("In NOTIFY_VITAL_PARAMETER requester %d\n",VITAL_DATA->REQUEST_ID);
#endif

	switch(VITAL_DATA->REQUEST_ID)
	{

	case GUI:
		setRequesterTimeStmap(Get_TimeStamp());
		setCommRequester(Get_Data_Requester());
		Update_GUIRequester(0);
		Check_Vital_Range(VITAL_DATA);
		xQueueSend(GUI_Queue, &(VITAL_DATA->VITAL_REQUESTED_PARAM), 0);

		Com_Send_Data(toSubID(VITAL_DATA->VITAL_REQUESTED_PARAM), NULL, 0, 0);

		if(VITAL_DATA->VITAL_REQUESTED_PARAM == ECG)
		{
			// Use optimized transfer for ECG samples
			Com_BufferXfer_Optimized(comm_wECGSamples,(uint8_t*)GLOBAL_DATA.ECG.filterEcPtr , 12000, 0);

			if(READ_USER_DETAILS(Raw_Data))// rawdata
			{
				// Use optimized transfer for raw ECG data (24KB)
				Com_BufferXfer_Optimized(comm_wEcgrawData, (uint8_t*)GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_ECGPtr, 24000, 0);
			}
		}

		break;

	case COMM:
		setRequesterTimeStmap(Get_TimeStamp());
		setCommRequester(Get_Data_Requester());

		Command_Status(1);

		Check_Vital_Range(VITAL_DATA);
		if(VITAL_DATA->VITAL_REQUESTED_PARAM == ECG)
		{
			// Use optimized transfer for COMM requests
			Com_BufferXfer_Optimized(comm_wECGSamples,(uint8_t*)GLOBAL_DATA.ECG.filterEcPtr, 12000, 0);
			if(READ_USER_DETAILS(Raw_Data))// rawdata
			{
				// Use optimized transfer for raw ECG data (24KB) - COMM request
				Com_BufferXfer_Optimized(comm_wEcgrawData, (uint8_t*)GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_ECGPtr, 24000, 0);
			}
			n_ecg_c++;
		}

		if(DataBaseCtx.listener == GUI)
			xQueueSend(GUI_Queue, &(VITAL_DATA->VITAL_REQUESTED_PARAM), 0);

		Reset_Database_Ctx();
		break;
	default:
		break;
	}

	return 1;
}

void RETRIEVE_VITAL_PARAMETER(SENSOR_DATA_STRUCT *VITAL_DATA)
{
	uint32_t currentTime = Get_TimeStamp();
	int32_t  err_no = getError();


	switch(VITAL_DATA->VITAL_REQUESTED_PARAM)
	{
	case Heart_Rate:
		if(VITAL_DATA->REQUEST_ID == COMM )
		{
			VITAL_DATA->vitals.HRM.hrmValue = (err_no == 0)? GLOBAL_DATA.HEART_RATE.hrmValue: err_no;
		}
		else
		{
			VITAL_DATA->vitals.HRM.hrmValue = GLOBAL_DATA.HEART_RATE.hrmValue;
		}
		VITAL_DATA->vitals.HRM.hrmC = GLOBAL_DATA.HEART_RATE.hrmC;
#if RAW_DATA
		VITAL_DATA->vitals.HRM.rawPpgGreenPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_HRPtr;
		VITAL_DATA->vitals.HRM.algo_Output = GLOBAL_DATA.RAW_SIGNALS_VITALS.algo_Output;
#endif
		VITAL_DATA->UNIX_TIME = currentTime;

		break;

	case SpO2:
		if(VITAL_DATA->REQUEST_ID == COMM )
		{
			VITAL_DATA->vitals.SPO2.spo2value = (err_no == 0)? GLOBAL_DATA.SPO2.spo2value: err_no;
		}
		else
		{
			VITAL_DATA->vitals.SPO2.spo2value = GLOBAL_DATA.SPO2.spo2value;
		}
		VITAL_DATA->vitals.SPO2.spo2C = GLOBAL_DATA.SPO2.spo2C;
#if RAW_DATA
		VITAL_DATA->vitals.SPO2.rawPpgAmbPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgAmb_SPO2Ptr;
		VITAL_DATA->vitals.SPO2.rawPpgIrPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgIr_SPO2Ptr;
		VITAL_DATA->vitals.SPO2.rawPpgRedPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgRed_SPO2Ptr;
		VITAL_DATA->vitals.SPO2.algoOutPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.algo_Output;
#endif
		VITAL_DATA->UNIX_TIME = currentTime;

		break;

	case Body_Temperature:
		VITAL_DATA->vitals.TEMP = GLOBAL_DATA.TEMPERATURE.cbt;
		VITAL_DATA->UNIX_TIME = GLOBAL_DATA.TEMPERATURE.time;

		break;

	case BP:
		if(VITAL_DATA->REQUEST_ID == COMM )
		{
			VITAL_DATA->vitals.BP.systolic = (err_no == 0)? GLOBAL_DATA.BP.systolic: err_no;
		}
		else
		{
			VITAL_DATA->vitals.BP.systolic = GLOBAL_DATA.BP.systolic;
		}
		VITAL_DATA->vitals.BP.diastolic = GLOBAL_DATA.BP.diastolic;
		VITAL_DATA->vitals.BP.PQRST_parameters.ECG_hr = GLOBAL_DATA.BP.PQRST_parameters.ECG_hr;
#if RAW_DATA
		VITAL_DATA->vitals.BP.rawppgGreenPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_BpPtr;
		VITAL_DATA->vitals.BP.rawEcgPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_BpPtr;
		VITAL_DATA->vitals.BP.algo_outPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.algo_Output;
#endif
		VITAL_DATA->UNIX_TIME = currentTime;
		break;

	case ECG:
		if(VITAL_DATA->REQUEST_ID == COMM )
		{
			VITAL_DATA->vitals.ECG.PQRST_parameters.Inferences = (err_no == 0)? GLOBAL_DATA.ECG.PQRST_parameters.Inferences: err_no;
		}
		else
		{
			VITAL_DATA->vitals.ECG.PQRST_parameters.Inferences = GLOBAL_DATA.ECG.PQRST_parameters.Inferences;
		}
		VITAL_DATA->vitals.ECG.ecgPtr = GLOBAL_DATA.ECG.ecgPtr;
		VITAL_DATA->vitals.ECG.filterEcPtr = GLOBAL_DATA.ECG.filterEcPtr;
		VITAL_DATA->vitals.ECG.PQRST_parameters.ECG_hr = GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr;
		VITAL_DATA->vitals.ECG.PQRST_parameters.pr  = GLOBAL_DATA.ECG.PQRST_parameters.pr;
		VITAL_DATA->vitals.ECG.PQRST_parameters.qrs = GLOBAL_DATA.ECG.PQRST_parameters.qrs;
		VITAL_DATA->vitals.ECG.PQRST_parameters.rr  = GLOBAL_DATA.ECG.PQRST_parameters.rr;
		VITAL_DATA->vitals.ECG.PQRST_parameters.qt  = GLOBAL_DATA.ECG.PQRST_parameters.qt;
		VITAL_DATA->UNIX_TIME = currentTime;
#if RAW_DATA
		VITAL_DATA->vitals.ECG.rawEcgPtr = GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_ECGPtr;
#endif
		break;
	case Battery_Percentage:
		VITAL_DATA->vitals.BTR_PRCNT = GLOBAL_DATA.BAT.BAT_PCNT;
		VITAL_DATA->UNIX_TIME = currentTime;
		break;

	case Pedometer_Step_Count:
		VITAL_DATA->vitals.STEP_CNT = GLOBAL_DATA.PEDOMETER.STEP_CNT;
		VITAL_DATA->UNIX_TIME = currentTime;
		break;

	default:
		break;

	}
}

unsigned int  RETRIEVE_ERROR(void)
{
	return AWT_errno;
}

uint32_t GET_VITAL_PARAMETER(SENSOR_DATA_STRUCT *VITAL_DATA)
{
	SENSOR_DATA_STRUCT* pVITAL_DATA;

	if(SENSOR_Queue == NULL || xSensorTskEvnt == NULL)
	{
		SensorQueueError++;
		return 0;
	}

	if(VITAL_DATA !=NULL)
	{

#ifdef DEBUG_PRINTS
		printf("In GET_VITAL_PARAMETER from UI \n");
#endif
		Set_Data_Requester(VITAL_DATA->REQUEST_ID);
		Set_Data_Parameter(VITAL_DATA->VITAL_REQUESTED_PARAM);
		Set_Data_Listener(VITAL_DATA->listener);
		Set_TimeStamp();
		pVITAL_DATA = VITAL_DATA;
	}
	else
	{
#ifdef DEBUG_PRINTS
		printf("In GET_VITAL_PARAMETER from wireless \n");
#endif
		pVITAL_DATA = &DataBaseCtx;
	}

#ifdef DEBUG_PRINTS
	printf("Requester %d Parameter %d Listener %d Time %ld \n",pVITAL_DATA->REQUEST_ID,pVITAL_DATA->VITAL_REQUESTED_PARAM,pVITAL_DATA->listener,pVITAL_DATA->UNIX_TIME);
#endif

	if(xQueueSend(SENSOR_Queue,pVITAL_DATA, 0) == pdTRUE )
	{
		xEventGroupSetBits(xSensorTskEvnt,EVENT_UI);

		if(pVITAL_DATA->REQUEST_ID == COMM)
		{
			if(pVITAL_DATA->VITAL_REQUESTED_PARAM == SpO2)
			{
				d_spo2_c++;
			}
			else if(pVITAL_DATA->VITAL_REQUESTED_PARAM == ECG)
			{
				d_ecg_c++;
			}
		}
	}
	else
	{

#ifdef DEBUG_PRINTS
		printf("In GET_VITAL_PARAMETER QueueSend failed \n");
#endif
		SensorQueueError++;
		return 0;
	}

#ifdef DEBUG_PRINTS
	printf("In GET_VITAL_PARAMETER exiting \n");
#endif
	return 1;
}


/********************** GLOBAL API'S for BATTERY, STEP COUNT ****************/

//function reads battery percentage and updates it to global data base
void readBatteryPercentage(void)
{
	GLOBAL_DATA.BAT.BAT_PCNT = currentBatteryStatus();
	GLOBAL_DATA.BAT.TIME_BAT_PCNT = Get_Current_Unix_Time();

	//Testing...
	UpdateBatteryValue(); //..(Have to call this UpdateBatteryValue fun from Periodic task)
}

void updateBatteryPercenatge(uint8_t bat)
{
	GLOBAL_DATA.BAT.BAT_PCNT = bat;
}

//function reads step count and updates it to global data base
uint32_t readStepCount(void)
{
	uint16_t steps = RRD_Pedo_status();
	if(steps > GLOBAL_DATA.PEDOMETER.STEP_CNT)
	{
		GLOBAL_DATA.PEDOMETER.STEP_CNT = steps;
		GLOBAL_DATA.PEDOMETER.TIME_STEP_CNT = Get_Current_Unix_Time();
		return 1;
	}
	return 0;
}

//Function writes the BLE is connected or not
void writeBleConnnectionStatus(uint8_t Status)
{
	//GLOBAL_DATA.BLE.BLE_CONNECTION = Status;

	if(Status == 1)
	{
		sendPopkey(DEVICE_CONNECT_BLE);
		UpdateBLEStatus(ble_connected);
		Set_Wireless_Status(ble_connected);

	}
	else
	{
		sendPopkey(DEVICE_DISCONNECT_BLE);
		UpdateBLEStatus(ble_disconnected);
		Set_Wireless_Status(ble_disconnected);
	}
}

//Function writes the BLE is Paired or not paired
void writeBlepairingStatus(uint8_t Status)
{
	//GLOBAL_DATA.BLE.BLE_PAIRING = Status;
	sendPopkey(DEVICE_PAIRED_BLE);
}

//Function reads whether the BLE is Paired or not
void readBlepairingStatus(uint32_t *Status)
{
	//*Status = GLOBAL_DATA.BLE.BLE_PAIRING ;
}

void RETRIEVE_BATTERY_PERCENTAGE(uint8_t *BATT)
{
	*BATT = GLOBAL_DATA.BAT.BAT_PCNT;
}

void RETRIEVE_STEP_COUNT(uint16_t *STEPS)
{
	*STEPS = GLOBAL_DATA.PEDOMETER.STEP_CNT;
}

uint8_t checkRequester(void)
{
	/*
	if(GLOBAL_DATA.BLE.BLE_Request)
		return BLE;
	else if(GLOBAL_DATA.Wifi.Wifi_Request)
		return WIFI;
	return GUI;
	 */
	return DataBaseCtx.REQUEST_ID;


}

void updateRequester(uint8_t requester,uint8_t value){

	DataBaseCtx.REQUEST_ID = value ?requester:ReqIDNONE;
	/*
	if(requester == BLE)
		GLOBAL_DATA.BLE.BLE_Request = value;
	else if(requester == WIFI)
		GLOBAL_DATA.Wifi.Wifi_Request = value;
	else{}
	 */
}

void clearGlobalData(void)
{
	ResetSteps();
	GLOBAL_DATA.HEART_RATE.hrmValue=0;
	GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE =0;
	GLOBAL_DATA.SPO2.spo2value=0;
	GLOBAL_DATA.SPO2.TIME_SPO2=0;
	GLOBAL_DATA.TEMPERATURE.cbt=0;
	GLOBAL_DATA.TEMPERATURE.time =0;
	GLOBAL_DATA.BP.systolic=0;
	GLOBAL_DATA.BP.diastolic=0;
	GLOBAL_DATA.BP.TIME_BLOOD_PRESSURE=0;
	GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr=0;
	GLOBAL_DATA.ECG.TIME_ECG=0;;
	GLOBAL_DATA.GSR.gsr=0;
	GLOBAL_DATA.GSR.TIME_GSR=0;
	GLOBAL_DATA.BP.PQRST_parameters.ECG_hr=0;
	memset(&GLOBAL_DATA.ECG.PQRST_parameters,0,sizeof(GLOBAL_DATA.ECG.PQRST_parameters));
	memset(&GLOBAL_DATA.HEART_RATE.HRM_HISTORY,0,sizeof(GLOBAL_DATA.HEART_RATE.HRM_HISTORY));
	memset(&GLOBAL_DATA.SPO2.SPO2_HISTORY,0,sizeof(GLOBAL_DATA.SPO2.SPO2_HISTORY));
	memset(&GLOBAL_DATA.BP.BP_HISTORY,0,sizeof(GLOBAL_DATA.BP.BP_HISTORY));
	memset(&GLOBAL_DATA.BP.TIME_BP,0,sizeof(GLOBAL_DATA.BP.TIME_BP));
	memset(&GLOBAL_DATA.TEMPERATURE.CBT_HISTORY,0,sizeof(GLOBAL_DATA.TEMPERATURE.CBT_HISTORY));
	memset(&GLOBAL_DATA,0,sizeof(GLOBAL_DATA));
}



uint8_t WiFI_Boot_Status()
{
	return 0; //GLOBAL_DATA.Wifi.wifi_boot_done_flag;
}

uint8_t Wifi_Status()
{
	return 0;//GLOBAL_DATA.Wifi.WIFISTATUS;
}

uint8_t BLE_Status()
{
	return 0;//GLOBAL_DATA.BLE.BLE_CONNECTION;
}
void Update_Wifi_Boot_Status(uint8_t status)
{
	//GLOBAL_DATA.Wifi.wifi_boot_done_flag = status;
}

void Update_Wifi_Status(uint8_t status)
{
	//GLOBAL_DATA.Wifi.WIFISTATUS = status;
	if(status == 1)
	{
		sendPopkey(WIFI_CONNECT);
		UpdateWIFIStatus(wifi_connected);
		Set_Wireless_Status(wifi_connected);

	}
	else
	{
		sendPopkey(WIFI_DISCONNECT);
		UpdateWIFIStatus(wifi_disconnected);
		Set_Wireless_Status(wifi_disconnected);
	}
}

void Update_Ble_Boot_Status(uint8_t status)
{
	//GLOBAL_DATA.BLE.ble_boot_done_flag = status;
}

void Update_Ble_Connection(uint8_t status){
	//GLOBAL_DATA.BLE.BLE_CONNECTION = status;
}

void Retrieve_Vital_Time(VITAL_PARAMETER_ID vital,uint32_t *value)
{
	switch(vital)
	{
	case Heart_Rate:
		*value =  GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE;
		break;
	case SpO2:
		*value =  GLOBAL_DATA.SPO2.TIME_SPO2;
		break;
	case Body_Temperature:
		*value =  GLOBAL_DATA.TEMPERATURE.time;
		break;
	default:
		break;
	}
}


void HRM_SPO2_BP_History(VITAL_PARAMETER_ID vital,uint8_t *value)
{
	switch (vital)
	{
	case Heart_Rate:
		memcpy(value,GLOBAL_DATA.HEART_RATE.HRM_HISTORY,sizeof(GLOBAL_DATA.HEART_RATE.HRM_HISTORY));
		break;
	case SpO2:
		memcpy(value,GLOBAL_DATA.SPO2.SPO2_HISTORY,sizeof(GLOBAL_DATA.SPO2.SPO2_HISTORY));
		break;
	case BP:
		memcpy(value,GLOBAL_DATA.BP.BP_HISTORY,sizeof(GLOBAL_DATA.BP.BP_HISTORY));
		break;
	default:
		break;
	}
}

void CBT_History(float *value)
{
	memcpy(value,GLOBAL_DATA.TEMPERATURE.CBT_HISTORY,sizeof(GLOBAL_DATA.TEMPERATURE.CBT_HISTORY));
}

void BP_History_Time(uint32_t *time)
{
	memcpy(time,GLOBAL_DATA.BP.TIME_BP,sizeof(GLOBAL_DATA.BP.TIME_BP));
}

void ECGParameters(ECG_PARAMETERS *param)
{
	param->ECG_hr = GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr;
	param->qrs = GLOBAL_DATA.ECG.PQRST_parameters.qrs;
	param->rr = GLOBAL_DATA.ECG.PQRST_parameters.rr;
	param->pr = GLOBAL_DATA.ECG.PQRST_parameters.pr;
	param->qt = GLOBAL_DATA.ECG.PQRST_parameters.qt;
	param->Inferences = GLOBAL_DATA.ECG.PQRST_parameters.Inferences;
}


void WriteGain1(uint8_t value)
{
	COPY_PERSISTENT_DATA(Gain1,value);
}

void WriteGain2(uint8_t value)
{
	COPY_PERSISTENT_DATA(Gain2,value);
}

void WriteRedLedCurrent(uint8_t value)
{
	COPY_PERSISTENT_DATA(Red_led_current,value);
}

void WriteGreenLedCurrent(uint8_t value)
{
	COPY_PERSISTENT_DATA(Green_led_current,value);
}

void WriteIRLedCurrent(uint8_t value)
{
	COPY_PERSISTENT_DATA(Ir_led_current,value);
}

// Write PD current
void WriteRedPDCurrent(uint8_t value)
{
	COPY_PERSISTENT_DATA(Red_pd_current,value);
}
void WriteGreenPDCurrent(uint8_t value)
{
	COPY_PERSISTENT_DATA(Green_pd_current,value);
}
void WriteIRPDCurrent(uint8_t value)
{
	COPY_PERSISTENT_DATA(Ir_pd_current,value);
}
uint8_t ReadGain1()
{
	return READ_PERSISTENT_DATA(Gain1);
}

uint8_t ReadGain2()
{
	return READ_PERSISTENT_DATA(Gain2);
}

uint8_t ReadRedLedCurrent()
{
	return READ_PERSISTENT_DATA(Red_led_current);
}

uint8_t ReadGreenLedCurrent()
{
	return READ_PERSISTENT_DATA(Green_led_current);
}

uint8_t ReadIRLedCurrent()
{
	return READ_PERSISTENT_DATA(Ir_led_current);
}
/// Read PD current
uint8_t ReadRedPDCurrent()
{
	return READ_PERSISTENT_DATA(Red_pd_current);
}
uint8_t ReadGreenPDCurrent()
{
	return READ_PERSISTENT_DATA(Green_pd_current);
}
uint8_t ReadIRPDCurrent()
{
	return READ_PERSISTENT_DATA(Ir_pd_current);
}
uint8_t Check_GUIRequester()
{
	//return (GLOBAL_DATA.GUI_Request)?0:1;


	return (Get_Data_Requester()== GUI)?1:0;


}

void Update_GUIRequester(uint8_t value)
{
	//GLOBAL_DATA.GUI_Request = value;

	REQUESTED_ID id = (value == 1 )?GUI: ReqIDNONE;
	Set_Data_Requester(id);



}

void Update_GSR_Value(uint32_t value)
{
	GLOBAL_DATA.GSR.gsr = value;
}

uint32_t ReadGSRValue()
{
	return GLOBAL_DATA.GSR.gsr;
}

void WriteStepcount(uint16_t value)
{
	GLOBAL_DATA.PEDOMETER.STEP_CNT = value;
}

void ResetSteps(void)
{
	GLOBAL_DATA.PEDOMETER.STEP_CNT = 0;
	GLOBAL_DATA.PEDOMETER.STEP_GOAL_STATUS = 0;
	GLOBAL_DATA.PEDOMETER.TIME_STEP_CNT = 0;
}

#define GOAL60  0x01
#define GOAL    0x02

uint32_t IsSteps60(void)
{
	return(GLOBAL_DATA.PEDOMETER.STEP_GOAL_STATUS & GOAL60 ? 1 : 0);
}

uint32_t IsStepsGoal()
{
	return(GLOBAL_DATA.PEDOMETER.STEP_GOAL_STATUS & GOAL ? 1 : 0);
}

void SetSteps60(void)
{
	GLOBAL_DATA.PEDOMETER.STEP_GOAL_STATUS |= GOAL60;
}

void SetStepsGoal()
{
	GLOBAL_DATA.PEDOMETER.STEP_GOAL_STATUS |= GOAL;
}

void BLEPasskeyPOPUP(void)
{
	if(!Get_Factory_Setting())
		sendHWKey(BLE_PASSKEY_SETUP);
	else
		sendPopkey(BLE_PASSKEY_POPUP);
}
void BLEPairedPOPUP(void)
{
	if(!Get_Factory_Setting())
		sendHWKey(BLE_PAIRED_SETUP);
	else
		sendPopkey(DEVICE_PAIRED_BLE);
}
void ConfigureLeadsOff(uint8_t value)
{
	COPY_PERSISTENT_DATA(Leads_Off,value);
}

uint8_t ReadLeadsOffStatus()
{
	return READ_PERSISTENT_DATA(Leads_Off);
}

uint8_t Read_HRMC()
{
	return GLOBAL_DATA.HEART_RATE.hrmC;
}

uint8_t Read_SPO2C()
{
	return GLOBAL_DATA.SPO2.spo2C;
}

void Check_Vital_Range(SENSOR_DATA_STRUCT *VITAL_DATA)
{
	switch(VITAL_DATA->VITAL_REQUESTED_PARAM)
	{

	case Heart_Rate:
	{
		uint8_t min,max;
		if(READ_USER_DETAILS(Vitals_Range_Alerts_Enable_CONTROL) == 1)
		{
			min = (READ_USER_DETAILS(HR_Changed_Normal[0]));
			max = (READ_USER_DETAILS(HR_Changed_Normal[1]));
		}
		else
		{
			min = (READ_USER_DETAILS(HR_Default_Normal[0]));
			max = (READ_USER_DETAILS(HR_Default_Normal[1]));
		}

		if(GLOBAL_DATA.HEART_RATE.hrmValue < min || GLOBAL_DATA.HEART_RATE.hrmValue > max)
		{
			sendPopkey(HRM_ABNORMAL);
		}
	}
	break;

	case SpO2:
	{
		uint8_t min,max;
		if(READ_USER_DETAILS(Vitals_Range_Alerts_Enable_CONTROL) == 1)
		{
			min = (READ_USER_DETAILS(SPO2_Changed_Normal[0]));
			max = (READ_USER_DETAILS(SPO2_Changed_Normal[1]));
		}
		else
		{
			min = (READ_USER_DETAILS(SPO2_Default_Normal[0]));
			max = (READ_USER_DETAILS(SPO2_Default_Normal[1]));
		}

		if(GLOBAL_DATA.SPO2.spo2value < min || GLOBAL_DATA.SPO2.spo2value > max)
		{
			sendPopkey(SPO2_ABNORMAL);
		}
	}
	break;
	case Body_Temperature:
	{
#if CBT_ENABLE
		float min,max;
		if(READ_USER_DETAILS(Vitals_Range_Alerts_Enable_CONTROL) == 1)
		{
			min = (READ_USER_DETAILS(CBT_Normal[0]))/100;
			max = (READ_USER_DETAILS(CBT_Normal[1]))/100;
		}
		else
		{
			min = (READ_USER_DETAILS(CBT_Default_Normal[0]));
			max = (READ_USER_DETAILS(CBT_Default_Normal[1]));
		}

		if(GLOBAL_DATA.TEMPERATURE.cbt < min || GLOBAL_DATA.TEMPERATURE.cbt > max)
		{
			sendPopkey(CBT_ABNORMAL);
		}
#endif
	}
	break;

	default:
		break;
	}

}


void WriteWirelessIndication(uint8_t value)
{
	COPY_PERSISTENT_DATA(Wireless_Indication,value);
}

uint8_t ReadWirelessIndication()
{
	return READ_PERSISTENT_DATA(Wireless_Indication);
}

void WriteStepGoal(uint16_t value)
{
	GLOBAL_DATA.PEDOMETER.STEP_GOAL = value;
}

uint16_t ReadStepGoal()
{
	return GLOBAL_DATA.PEDOMETER.STEP_GOAL;
}

