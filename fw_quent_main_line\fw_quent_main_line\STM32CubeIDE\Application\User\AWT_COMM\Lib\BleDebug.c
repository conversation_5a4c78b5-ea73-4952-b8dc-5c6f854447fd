#include "BleDebug.h"
#include "FlowControl.h"
#include "PerformanceMonitor.h"
#include "OptimizedTransfer.h"
#include <stdio.h>
#include <stdlib.h>

// Debug configuration
static ble_debug_config_t g_debug_config = {
    .enable_timing_logs = 1,
    .enable_throughput_logs = 1,
    .enable_packet_logs = 0,  // Disabled by default to reduce spam
    .log_level = BLE_DEBUG_LEVEL_INFO
};

void ble_debug_init(void) {
    printf("BLE Debug System Initialized\n");
    printf("Timing logs: %s\n", g_debug_config.enable_timing_logs ? "ENABLED" : "DISABLED");
    printf("Throughput logs: %s\n", g_debug_config.enable_throughput_logs ? "ENABLED" : "DISABLED");
    printf("Packet logs: %s\n", g_debug_config.enable_packet_logs ? "ENABLED" : "DISABLED");
}

void ble_debug_set_config(ble_debug_config_t* config) {
    if (config != NULL) {
        g_debug_config = *config;
        printf("BLE Debug configuration updated\n");
    }
}

void ble_debug_log_packet_timing(uint8_t packet_num, uint32_t time_ms) {
    if (!g_debug_config.enable_timing_logs) return;
    
    if (g_debug_config.log_level <= BLE_DEBUG_LEVEL_DEBUG) {
        printf("PKT[%d]: %d ms\n", packet_num, time_ms);
    }
    
    // Warn about slow packets
    if (time_ms > 1000 && g_debug_config.log_level <= BLE_DEBUG_LEVEL_WARN) {
        printf("WARNING: Packet %d took %d ms (>1 second)\n", packet_num, time_ms);
    }
    
    // Critical alert for very slow packets
    if (time_ms > 5000 && g_debug_config.log_level <= BLE_DEBUG_LEVEL_ERROR) {
        printf("CRITICAL: Packet %d took %d ms (>5 seconds)\n", packet_num, time_ms);
    }
}

void ble_debug_log_throughput(uint32_t bytes, uint32_t time_ms) {
    if (!g_debug_config.enable_throughput_logs) return;
    
    if (time_ms == 0) return;  // Avoid division by zero
    
    uint32_t throughput_bps = (bytes * 1000) / time_ms;
    uint32_t throughput_kbps = throughput_bps / 1000;
    
    if (g_debug_config.log_level <= BLE_DEBUG_LEVEL_INFO) {
        printf("THROUGHPUT: %d bytes in %d ms = %d KB/s (%d B/s)\n", 
               bytes, time_ms, throughput_kbps, throughput_bps);
    }
    
    // Warn about low throughput
    if (throughput_kbps < 5 && g_debug_config.log_level <= BLE_DEBUG_LEVEL_WARN) {
        printf("WARNING: Low throughput detected: %d KB/s\n", throughput_kbps);
    }
}

void ble_debug_log_transfer_start(uint32_t total_bytes, uint16_t total_packets) {
    if (g_debug_config.log_level <= BLE_DEBUG_LEVEL_INFO) {
        printf("\n=== TRANSFER START ===\n");
        printf("Total bytes: %d\n", total_bytes);
        printf("Total packets: %d\n", total_packets);
        printf("Average packet size: %d bytes\n", total_bytes / total_packets);
        printf("=====================\n");
    }
}

void ble_debug_log_transfer_end(uint32_t total_time_ms, uint32_t total_bytes) {
    if (g_debug_config.log_level <= BLE_DEBUG_LEVEL_INFO) {
        printf("\n=== TRANSFER COMPLETE ===\n");
        printf("Total time: %d ms\n", total_time_ms);
        printf("Total bytes: %d\n", total_bytes);
        
        if (total_time_ms > 0) {
            uint32_t throughput = (total_bytes * 1000) / total_time_ms;
            printf("Overall throughput: %d B/s (%d KB/s)\n", 
                   throughput, throughput / 1000);
        }
        printf("========================\n\n");
    }
}

void ble_debug_test_small_transfer(void) {
    printf("Testing small transfer (500 bytes)...\n");
    
    uint8_t* test_data = malloc(500);
    if (test_data == NULL) {
        printf("Failed to allocate test data\n");
        return;
    }
    
    // Fill with test pattern
    for (int i = 0; i < 500; i++) {
        test_data[i] = i & 0xFF;
    }
    
    uint32_t start_time = HAL_GetTick();
    int32_t result = Com_BufferXfer_Optimized(comm_wEcgrawData, test_data, 500, NULL);
    uint32_t end_time = HAL_GetTick();
    
    if (result == 0) {
        ble_debug_log_throughput(500, end_time - start_time);
    } else {
        printf("Small transfer test failed: %d\n", result);
    }
    
    free(test_data);
}

void ble_debug_test_large_transfer(void) {
    printf("Testing large transfer (24KB)...\n");
    
    uint8_t* test_data = malloc(24000);
    if (test_data == NULL) {
        printf("Failed to allocate test data\n");
        return;
    }
    
    // Fill with test pattern
    for (int i = 0; i < 24000; i++) {
        test_data[i] = i & 0xFF;
    }
    
    uint32_t start_time = HAL_GetTick();
    int32_t result = Com_BufferXfer_Optimized(comm_wEcgrawData, test_data, 24000, NULL);
    uint32_t end_time = HAL_GetTick();
    
    if (result == 0) {
        ble_debug_log_throughput(24000, end_time - start_time);
    } else {
        printf("Large transfer test failed: %d\n", result);
    }
    
    free(test_data);
}

void ble_debug_run_performance_tests(void) {
    printf("\n=== BLE PERFORMANCE TESTS ===\n");
    
    // Test 1: Small transfer
    ble_debug_test_small_transfer();
    HAL_Delay(2000);  // Wait between tests
    
    // Test 2: Large transfer
    ble_debug_test_large_transfer();
    HAL_Delay(2000);
    
    // Test 3: Multiple small transfers
    printf("Testing multiple small transfers...\n");
    uint32_t total_start = HAL_GetTick();
    for (int i = 0; i < 5; i++) {
        ble_debug_test_small_transfer();
        HAL_Delay(500);
    }
    uint32_t total_end = HAL_GetTick();
    
    printf("5 small transfers completed in %d ms\n", total_end - total_start);
    
    // Print final statistics
    optimized_transfer_print_statistics();
    
    printf("=== TESTS COMPLETE ===\n\n");
}

void ble_debug_monitor_connection(void) {
    static uint32_t last_check = 0;
    uint32_t current_time = HAL_GetTick();
    
    // Check every 5 seconds
    if (current_time - last_check > 5000) {
        last_check = current_time;
        
        // Check BLE connection status
        if (Get_Wireless_Status() == ble_connected) {
            printf("BLE Status: CONNECTED\n");
        } else {
            printf("BLE Status: DISCONNECTED\n");
        }
        
        // Print performance statistics
        if (g_debug_config.enable_throughput_logs) {
            flow_control_print_stats();
        }
    }
}

void ble_debug_enable_packet_logs(uint8_t enable) {
    g_debug_config.enable_packet_logs = enable;
    printf("Packet logging %s\n", enable ? "ENABLED" : "DISABLED");
}

void ble_debug_set_log_level(ble_debug_level_t level) {
    g_debug_config.log_level = level;
    printf("Debug log level set to %d\n", level);
}
