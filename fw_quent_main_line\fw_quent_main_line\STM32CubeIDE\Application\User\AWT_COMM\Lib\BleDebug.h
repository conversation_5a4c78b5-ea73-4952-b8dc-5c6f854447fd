#ifndef BLE_DEBUG_H
#define BLE_DEBUG_H

#include <stdint.h>
#include "stm32l4xx_hal.h"
#include "watch_state.h"
#include "Wireless.h"

// Debug levels
typedef enum {
    BLE_DEBUG_LEVEL_ERROR = 0,
    BLE_DEBUG_LEVEL_WARN = 1,
    BLE_DEBUG_LEVEL_INFO = 2,
    BLE_DEBUG_LEVEL_DEBUG = 3
} ble_debug_level_t;

// Debug configuration structure
typedef struct {
    uint8_t enable_timing_logs;
    uint8_t enable_throughput_logs;
    uint8_t enable_packet_logs;
    ble_debug_level_t log_level;
} ble_debug_config_t;

// Function prototypes
void ble_debug_init(void);
void ble_debug_set_config(ble_debug_config_t* config);
void ble_debug_log_packet_timing(uint8_t packet_num, uint32_t time_ms);
void ble_debug_log_throughput(uint32_t bytes, uint32_t time_ms);
void ble_debug_log_transfer_start(uint32_t total_bytes, uint16_t total_packets);
void ble_debug_log_transfer_end(uint32_t total_time_ms, uint32_t total_bytes);
void ble_debug_test_small_transfer(void);
void ble_debug_test_large_transfer(void);
void ble_debug_run_performance_tests(void);
void ble_debug_monitor_connection(void);
void ble_debug_enable_packet_logs(uint8_t enable);
void ble_debug_set_log_level(ble_debug_level_t level);

// Convenience macros
#define BLE_DEBUG_PACKET_TIME(packet, time) ble_debug_log_packet_timing(packet, time)
#define BLE_DEBUG_THROUGHPUT(bytes, time) ble_debug_log_throughput(bytes, time)
#define BLE_DEBUG_TRANSFER_START(bytes, packets) ble_debug_log_transfer_start(bytes, packets)
#define BLE_DEBUG_TRANSFER_END(time, bytes) ble_debug_log_transfer_end(time, bytes)

#endif // BLE_DEBUG_H
