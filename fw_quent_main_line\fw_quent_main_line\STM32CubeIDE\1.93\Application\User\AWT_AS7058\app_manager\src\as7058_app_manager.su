as7058_app_manager.c:694:19:process_measurement_config	16	static
as7058_app_manager.c:710:19:clear_measurement_config	8	static
as7058_app_manager.c:727:19:get_sample_period_for_channel	24	static
as7058_app_manager.c:761:19:extract_samples_from_fifo_data	32	static
as7058_app_manager.c:778:17:get_sample_count_for_channel	24	static
as7058_app_manager.c:794:19:get_adc_value_of_sample	32	static
as7058_app_manager.c:818:19:get_pd_offset_of_sample	32	static
as7058_app_manager.c:851:1:configure_pd_offset_compensation	32	static
as7058_app_manager.c:905:19:perform_pd_offset_compensation	40	static
as7058_app_manager.c:951:17:convert_sample_u16	16	static
as7058_app_manager.c:965:17:convert_sample_u32	16	static
as7058_app_manager.c:974:16:convert_sample_i32	16	static
as7058_app_manager.c:985:19:get_internal_app_id_for_public_app_id	24	static
as7058_app_manager.c:1012:27:generate_agc_change_state	16	static
as7058_app_manager.c:1035:19:generate_agc_status	24	static
as7058_app_manager.c:1101:40:generate_metadata	24	static
as7058_app_manager.c:1151:19:get_agc_status_of_channel	32	static
as7058_app_manager.c:1189:19:get_sample_with_metadata	48	static
as7058_app_manager.c:1216:19:perform_enabled_preprocessing	32	static
as7058_app_manager.c:1251:19:process_samples	936	static
as7058_app_manager.c:1496:16:check_agc_statuses_change	32	static
as7058_app_manager.c:1574:16:check_status_events_change	40	static
as7058_app_manager.c:1599:19:handle_raw_app_start	24	static
as7058_app_manager.c:1614:19:handle_raw_app_set_input	48	static
as7058_app_manager.c:1630:19:handle_bio_stream_start	24	static
as7058_app_manager.c:1643:19:handle_bio_stream_set_input	80	static
as7058_app_manager.c:1723:36:as7058_appmgr_initialize	24	static
as7058_app_manager.c:1779:36:as7058_appmgr_enable_apps	40	static
as7058_app_manager.c:1816:36:as7058_appmgr_set_signal_routing	32	static
as7058_app_manager.c:1853:36:as7058_appmgr_configure_app	32	static
as7058_app_manager.c:1869:36:as7058_appmgr_enable_preprocessing	32	static
as7058_app_manager.c:1898:12:as7058_appmgr_configure_preprocessing	24	static
as7058_app_manager.c:1922:36:as7058_appmgr_start_processing	64	static
as7058_app_manager.c:2016:36:as7058_appmgr_set_input	160	static
as7058_app_manager.c:2095:36:as7058_appmgr_set_special_measurement_input	32	static
as7058_app_manager.c:2126:36:as7058_appmgr_set_ext_event_occurred	4	static
as7058_app_manager.c:2138:36:as7058_appmgr_execute	32	static
as7058_app_manager.c:2171:36:as7058_appmgr_get_output	40	static
as7058_app_manager.c:2192:36:as7058_appmgr_stop_processing	24	static
as7058_app_manager.c:2217:36:as7058_appmgr_shutdown	24	static
as7058_app_manager.c:2240:37:as7058_appmgr_get_version	4	static
