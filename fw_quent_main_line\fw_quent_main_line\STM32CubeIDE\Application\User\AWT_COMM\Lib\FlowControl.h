#ifndef FLOW_CONTROL_H
#define FLOW_CONTROL_H

#include <stdint.h>
#include "stm32l4xx_hal.h"

// Flow control configuration
#define MIN_DELAY_MS           1
#define MAX_DELAY_MS           50
#define DEFAULT_DELAY_MS       5
#define ACK_TIMEOUT_MS         1000
#define MAX_CONSECUTIVE_TIMEOUTS 3
#define FLOW_CONTROL_WINDOW    10  // Number of packets to average

// Flow control states
typedef enum {
    FLOW_STATE_NORMAL,
    FLOW_STATE_CONGESTED,
    FLOW_STATE_TIMEOUT,
    FLOW_STATE_OPTIMIZED
} flow_state_t;

// Flow control statistics
typedef struct {
    uint32_t last_ack_time;
    uint32_t avg_ack_time;
    uint32_t min_ack_time;
    uint32_t max_ack_time;
    uint16_t consecutive_timeouts;
    uint16_t consecutive_fast_acks;
    uint16_t optimal_delay;
    uint16_t packets_sent;
    uint16_t packets_acked;
    flow_state_t current_state;
    uint32_t ack_times[FLOW_CONTROL_WINDOW];
    uint8_t ack_index;
} flow_control_t;

// Transfer mode for different data sizes
typedef enum {
    TRANSFER_MODE_NORMAL,    // < 1KB
    TRANSFER_MODE_BURST,     // 1KB - 10KB
    TRANSFER_MODE_STREAMING  // > 10KB
} transfer_mode_t;

// Function prototypes
void flow_control_init(void);
void flow_control_reset(void);
uint16_t flow_control_get_delay(uint32_t data_size);
void flow_control_update_ack_time(uint32_t ack_time);
void flow_control_timeout_occurred(void);
transfer_mode_t flow_control_get_transfer_mode(uint32_t data_size);
void flow_control_optimize_for_mode(transfer_mode_t mode);
void flow_control_print_stats(void);

// Inline helper functions
static inline uint32_t flow_control_get_current_time(void) {
    return HAL_GetTick();
}

static inline uint32_t flow_control_calculate_throughput(uint32_t bytes, uint32_t time_ms) {
    if (time_ms == 0) return 0;
    return (bytes * 1000) / time_ms;  // bytes per second
}

#endif // FLOW_CONTROL_H
