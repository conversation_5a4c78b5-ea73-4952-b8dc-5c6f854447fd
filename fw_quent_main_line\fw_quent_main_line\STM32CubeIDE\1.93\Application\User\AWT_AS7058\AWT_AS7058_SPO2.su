AWT_AS7058_SPO2.c:93:13:as7058_callback	48	static
AWT_AS7058_SPO2.c:128:14:As7058_Spo2_Init	320	static
AWT_AS7058_SPO2.c:545:6:Spo2_Outlier_Elimination	64	static
AWT_AS7058_SPO2.c:594:7:As7058_Spo2_Data_Collection	48	static
AWT_AS7058_SPO2.c:720:7:As7058_Spo2_Data_Collection_C	48	static
AWT_AS7058_SPO2.c:844:7:As7058_Spo2_Start_Measurement	16	static
AWT_AS7058_SPO2.c:864:7:As7058_Spo2_Stop_Measurement	16	static
AWT_AS7058_SPO2.c:883:7:As7058_Spo2_DeInit	16	static
AWT_AS7058_SPO2.c:908:7:As7058_Spo2	32	static
AWT_AS7058_SPO2.c:977:17:<PERSON><PERSON>O2Samples	24	static
AWT_AS7058_SPO2.c:1039:17:<PERSON><PERSON><PERSON><PERSON>02	32	static
