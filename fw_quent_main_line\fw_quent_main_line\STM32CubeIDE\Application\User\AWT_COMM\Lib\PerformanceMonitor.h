#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include <stdint.h>
#include "stm32l4xx_hal.h"

// Performance monitoring configuration
#define PERF_MONITOR_ENABLED        1
#define PERF_HISTORY_SIZE          10
#define PERF_WARNING_THRESHOLD_MS  1000  // Warn if packet takes >1 second
#define PERF_CRITICAL_THRESHOLD_MS 5000  // Critical if packet takes >5 seconds

// Performance metrics structure
typedef struct {
    uint32_t transfer_start_time;
    uint32_t transfer_end_time;
    uint32_t total_bytes;
    uint32_t total_packets;
    uint32_t successful_packets;
    uint32_t failed_packets;
    uint32_t retransmissions;
    uint32_t avg_packet_time;
    uint32_t min_packet_time;
    uint32_t max_packet_time;
    uint32_t effective_throughput;  // bytes per second
    uint32_t packet_times[PERF_HISTORY_SIZE];
    uint8_t packet_index;
    uint8_t is_active;
} performance_metrics_t;

// Transfer statistics for different data sizes
typedef struct {
    uint32_t small_transfers;    // < 1KB
    uint32_t medium_transfers;   // 1KB - 10KB  
    uint32_t large_transfers;    // > 10KB
    uint32_t total_bytes_sent;
    uint32_t total_time_ms;
} transfer_statistics_t;

// Function prototypes
void perf_monitor_init(void);
void perf_monitor_start_transfer(uint32_t total_bytes, uint32_t total_packets);
void perf_monitor_end_transfer(void);
void perf_monitor_packet_sent(void);
void perf_monitor_packet_acked(uint32_t packet_time);
void perf_monitor_packet_failed(void);
void perf_monitor_retransmission(void);
void perf_monitor_print_stats(void);
void perf_monitor_print_summary(void);
uint32_t perf_monitor_get_avg_throughput(void);
uint8_t perf_monitor_is_performance_degraded(void);

// Inline helper functions
static inline uint32_t perf_monitor_get_time(void) {
    return HAL_GetTick();
}

static inline uint32_t perf_monitor_calculate_throughput(uint32_t bytes, uint32_t time_ms) {
    if (time_ms == 0) return 0;
    return (bytes * 1000) / time_ms;
}

// Macros for conditional compilation
#if PERF_MONITOR_ENABLED
    #define PERF_START_TRANSFER(bytes, packets) perf_monitor_start_transfer(bytes, packets)
    #define PERF_END_TRANSFER() perf_monitor_end_transfer()
    #define PERF_PACKET_SENT() perf_monitor_packet_sent()
    #define PERF_PACKET_ACKED(time) perf_monitor_packet_acked(time)
    #define PERF_PACKET_FAILED() perf_monitor_packet_failed()
    #define PERF_RETRANSMISSION() perf_monitor_retransmission()
#else
    #define PERF_START_TRANSFER(bytes, packets)
    #define PERF_END_TRANSFER()
    #define PERF_PACKET_SENT()
    #define PERF_PACKET_ACKED(time)
    #define PERF_PACKET_FAILED()
    #define PERF_RETRANSMISSION()
#endif

#endif // PERFORMANCE_MONITOR_H
