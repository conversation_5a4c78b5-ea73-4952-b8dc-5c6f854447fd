#include "OptimizedTransfer.h"
#include "FlowControl.h"
#include "PerformanceMonitor.h"
#include "Comm_Manager.h"
#include "SerialInterface.h"
#include <stdio.h>
#include <string.h>

// Static variables for optimized transfer
static uint8_t g_transfer_active = 0;
static uint32_t g_transfer_start_time = 0;

int32_t Com_BufferXfer_Optimized(enum comm_subid subid, uint8_t* data, uint16_t len, com_callback callback)
{
    // Check if communication is connected
    int32_t comm_state = isCommConnected();
    int32_t nw_subid = isNwDependentSubid(subid);
    
    if((comm_state == 0) && (nw_subid == 1)) {
        return -1;
    }
    
    // Initialize flow control and performance monitoring
    if (!g_transfer_active) {
        flow_control_init();
        perf_monitor_init();
        g_transfer_active = 1;
        g_transfer_start_time = HAL_GetTick();
    }
    
    // Determine transfer mode based on data size
    transfer_mode_t mode = flow_control_get_transfer_mode(len);
    flow_control_optimize_for_mode(mode);
    
    // Calculate number of packets
    uint16_t mtu = getMtuSize(subid);
    uint16_t payload_size = mtu - BUFF_TX_HDR;
    uint16_t total_packets = (len + payload_size - 1) / payload_size;
    
    // Start performance monitoring
    PERF_START_TRANSFER(len, total_packets);
    
    printf("Starting optimized transfer: %d bytes, %d packets, mode: %d\n", 
           len, total_packets, mode);
    
    // Prepare message for communication manager
    Comm_Queue_t msg;
    memset(&msg, 0, sizeof(msg));
    
    msg.module = ul;
    msg.event.pt.sub_id = subid;
    msg.event.pt.payload = data;
    msg.event.pt.len = len;
    msg.event.pt.trigger = buff_tx;
    msg.event.pt.retry = 3;
    msg.event.pt.function = callback;
    
    // Set transfer mode specific parameters
    switch(mode) {
        case TRANSFER_MODE_NORMAL:
            msg.event.pt.retry = 3;
            break;
            
        case TRANSFER_MODE_BURST:
            msg.event.pt.retry = 2;  // Fewer retries for faster throughput
            break;
            
        case TRANSFER_MODE_STREAMING:
            msg.event.pt.retry = 1;  // Minimal retries for maximum speed
            break;
    }
    
    COM_PRINTI("Optimized Com_BufferXfer -> triggerCommMngr %d (mode: %d)\n", subid, mode);
    
    int32_t result = triggerCommMngr(&msg);
    
    if (result == 0) {
        // Transfer completed successfully
        uint32_t transfer_time = HAL_GetTick() - g_transfer_start_time;
        uint32_t throughput = flow_control_calculate_throughput(len, transfer_time);
        
        printf("Transfer completed: %d ms, throughput: %d bytes/sec\n", 
               transfer_time, throughput);
        
        PERF_END_TRANSFER();
        flow_control_print_stats();
        perf_monitor_print_stats();
        
        g_transfer_active = 0;
    }
    
    return result;
}

void optimized_transfer_packet_sent(uint8_t segment_num, uint16_t total_segments)
{
    PERF_PACKET_SENT();
    
    // Calculate progress
    uint8_t progress = (segment_num * 100) / total_segments;
    
    if (segment_num % 10 == 0 || segment_num == total_segments) {
        printf("Transfer progress: %d%% (%d/%d packets)\n", 
               progress, segment_num, total_segments);
    }
}

void optimized_transfer_packet_acked(uint32_t ack_time)
{
    PERF_PACKET_ACKED(ack_time);
    flow_control_update_ack_time(ack_time);
    
    // Check for performance degradation
    if (perf_monitor_is_performance_degraded()) {
        printf("WARNING: Performance degradation detected!\n");
        // Could trigger additional optimizations here
    }
}

void optimized_transfer_packet_failed(void)
{
    PERF_PACKET_FAILED();
    flow_control_timeout_occurred();
    
    printf("Packet transmission failed, adjusting flow control\n");
}

void optimized_transfer_retransmission(void)
{
    PERF_RETRANSMISSION();
    printf("Packet retransmission occurred\n");
}

uint32_t optimized_transfer_get_recommended_delay(uint32_t data_size)
{
    return flow_control_get_delay(data_size);
}

void optimized_transfer_print_statistics(void)
{
    printf("\n=== Optimized Transfer Statistics ===\n");
    flow_control_print_stats();
    printf("\n");
    perf_monitor_print_summary();
    printf("=====================================\n\n");
}

// Test function for throughput measurement
void test_optimized_throughput(void)
{
    printf("Testing optimized BLE throughput...\n");
    
    // Test with different data sizes
    uint16_t test_sizes[] = {500, 1000, 5000, 12000, 24000};
    uint8_t num_tests = sizeof(test_sizes) / sizeof(test_sizes[0]);
    
    for (uint8_t i = 0; i < num_tests; i++) {
        uint8_t* test_data = malloc(test_sizes[i]);
        if (test_data == NULL) {
            printf("Failed to allocate test data for size %d\n", test_sizes[i]);
            continue;
        }
        
        // Fill with test pattern
        for (uint16_t j = 0; j < test_sizes[i]; j++) {
            test_data[j] = j & 0xFF;
        }
        
        printf("\nTesting %d bytes transfer...\n", test_sizes[i]);
        uint32_t start_time = HAL_GetTick();
        
        int32_t result = Com_BufferXfer_Optimized(comm_wEcgrawData, test_data, test_sizes[i], NULL);
        
        uint32_t end_time = HAL_GetTick();
        uint32_t duration = end_time - start_time;
        
        if (result == 0) {
            uint32_t throughput = flow_control_calculate_throughput(test_sizes[i], duration);
            printf("Test %d: %d bytes in %d ms = %d bytes/sec\n", 
                   i+1, test_sizes[i], duration, throughput);
        } else {
            printf("Test %d failed with result: %d\n", i+1, result);
        }
        
        free(test_data);
        HAL_Delay(1000);  // Wait between tests
    }
    
    optimized_transfer_print_statistics();
}
