core_cm4.h:1717:22:__NVIC_DisableIRQ	16	static,ignoring_inline_asm
core_cm4.h:1933:34:__NVIC_SystemReset	4	static,ignoring_inline_asm
main.c:129:22:<PERSON><PERSON><PERSON><PERSON>Connected	4	static
main.c:140:5:main	24	static
main.c:172:6:QuentSysInit	16	static
main.c:262:6:Handle_Shutdown_Restart	16	static
main.c:303:6:<PERSON><PERSON>_HW_Restart	16	static
main.c:339:6:<PERSON><PERSON>_SW_Restart	16	static
main.c:379:6:<PERSON><PERSON>_Sleep_Restart	16	static
main.c:419:13:<PERSON>le_Charging	16	static
main.c:450:6:Init_PMIC	16	static
main.c:464:6:Init_Acc	8	static
main.c:471:6:Init_Display	8	static
main.c:490:18:GetWakeupTrig	48	static
main.c:553:6:vApplicationStackOverflowHook	16	static
main.c:562:6:<PERSON><PERSON>_<PERSON><PERSON>_Init	72	static
main.c:712:6:<PERSON><PERSON>_GPIO_INIT	32	static
main.c:734:6:Disable_Uart	16	static
main.c:748:6:<PERSON><PERSON>ui<PERSON>ask	16	static
main.c:759:6:Register_Sensor_Callback	16	static
main.c:764:6:DeRegister_Sensor_Callback	4	static
main.c:769:6:HomeKey_IRQ	8	static
main.c:800:6:Register_PMIC_Callback	16	static
main.c:805:6:DeRegister_PMIC_Callback	4	static
main.c:811:6:PMIC_IRQ	8	static
main.c:818:6:BLE_IRQ	8	static
main.c:836:6:WiFi_IRQ	8	static
main.c:854:6:Sensor_IRQ	8	static
main.c:864:6:HAL_GPIO_EXTI_Callback	16	static
main.c:898:6:uart_reinit	16	static
main.c:910:6:flash_wifi	8	static
main.c:918:6:power_up_wifi	8	static
main.c:923:6:power_down_wifi	8	static
main.c:932:20:Save_Shutdown_Time	16	static
main.c:938:6:Low_Voltage_Shutdown	8	static
main.c:975:6:Device_Shutdown_mode	8	static
main.c:1021:6:Device_Standby_mode	8	static
main.c:1079:6:Device_Charge_Mode	16	static
main.c:1103:6:enableOcare	32	static
main.c:1120:6:disableOcare	32	static
main.c:1142:6:enableSensor	32	static
main.c:1169:6:disableSensor	32	static
main.c:1203:6:Display_Pin_Conf	32	static
main.c:1241:6:LCD_PowerOff	40	static
main.c:1274:6:LCD_PowerOn	8	static
main.c:1285:6:Set_ResetCause	16	static
main.c:1290:6:TP_Init	32	static
main.c:1318:6:disableAccelerometer	32	static
main.c:1340:6:updateOTAStatus	16	static
main.c:1365:6:savetortc	24	static
main.c:1379:10:Get_Ble_Disconnection_reason	8	static
main.c:1386:5:_write	24	static
main.c:3811:6:Display_Update_Image	40	static
