/*
 * Comm_Manager.h
 *
 *  Created on: 06-Jan-2023
 *      Author: admin
 */

#ifndef _COMM_MANAGER_H_
#define _COMM_MANAGER_H_
#define TRANSPORT_FAIL
#include "Awt_types.h"
#include "Wireless.h"
#include "SerialInterface.h"

# define CRTIME 			60000
# define ITIME 			    30000 // only for debugging
# define BTIME              0xFFFFFFFF

#define MAX_MASTER_RETRIES     5
#define STATE_CHANGE           1
#define STATE_WAIT             0
#define WHEADER	               0xBE
#define WFOOTER	               0xFE
#define HEADER_HDR			   0xBE
#define HEADER_FTR			   0xFE
#define HDR_SIZE	           0x06
#define FTR_SIZE               0x05
#define ACK                    0x06
#define NACK                   0x15

#define CUR_SEG 			   0x00
#define MTU_LSB 			   0x01
#define MTU_MSB 			   0x02
#define TOTAL_LEN_LSB0 		   0x03
#define	TOTAL_LEN_LSB1 		   0x04
#define TOTAL_LEN_MSB 		   0x05
#define TIMESTAMP_LSB0		   0x06
#define TIMESTAMP_LSB1		   0x07
#define TIMESTAMP_MSB0		   0x08
#define TIMESTAMP_MSB1		   0x09
#define DATA_ORIGIN			   0x0A
#define CRC_LSB0		       0x0B
#define CRC_LSB1		       0x0C
#define CRC_MSB0		       0x0D
#define CRC_MSB1		       0x0E
#define BUFF_TX_HDR 15

// Burst transmission configuration
#define BURST_SIZE_SMALL       3    // 3 packets for small transfers
#define BURST_SIZE_LARGE       8    // 8 packets for large transfers
#define BURST_THRESHOLD        5000 // 5KB threshold for burst mode
#define MAX_CONSECUTIVE_BURSTS 5    // Max bursts without ACK check


enum _Exchange_Type{COMMAND,RESPONSE,INDICATION,BUFFERTX,comACK};


#pragma pack(1)
struct _Exchange
{
	uint8_t  Header;
	uint8_t  ID;
	uint16_t SUB_ID;
	uint16_t size;
	uint8_t  Data[MAX_DATA];
};

#pragma pack(1)
struct footer
{
	uint32_t crc;
	uint8_t footer;
};

#pragma pack(1)
struct Buffer_Xfer
{
	uint8_t *payload;
	uint8_t  current_segment;
	uint8_t  max_segments; //Total number of segments to be sent in buffer transfer data
	uint16_t mtu;
	uint32_t tot_len;
	uint32_t timestamp;
	uint8_t  requester;
	uint32_t crc;
};

typedef struct _Exchange Command;
typedef struct _Exchange Response;
typedef struct _Exchange Indication;

enum _ttrigger
{
	tx_event 	   = 0x00,
	st_wakeup 	   = 0x01,
	tx_comp		   = 0x02,
	rx_event	   = 0x03,
	peer_close 	   = 0x04,
	t_close_event  = 0x05,
	tchange_state  = 0x06,
	t_ack,
	t_nack,
	t_break,
	t_timeout,
	t_invalid
};

enum _ptrigger
{
	cmd_tx,   // transmit command
	cmd_f,    // command fail(received NACK in Transport)
	cmd_s,    // command success (received ACK in Transport)
	resp_r,   // response received
	cr_ack_s, // command.response ack sent
	cr_nack_s, // command.response nack sent
	cr_exec_s, // command/response execute success
	cr_exec_f, // command/response execute fail
	cmd_r,    // command received
	rsp_s,    // response sent success
	rsp_f,     //  response send fail
	cr_transp_f, // command/response transport fail
	p_cr_change_state, // command/response change state
	p_cr_timeout,		// command/response timeout
	ind_tx,   // transmit indication
	ind_r,     // receive indication
	ind_s,     // indication send success
	ind_f,     // indication send fail
	ind_ack_s, // indication ack sent
	ind_nack_s, // indication nack sent
	ind_exec_s,    // indication execute state success
	ind_exec_f,    // indication execute state fail
	buff_tx,
	bufferxfer_rx,
	bufferxfer_s,	  //  indication bulk success
	bufferxfer_f,	   // inication bulk fail
	bufferxfer_exec_s, //Buffer transfer execute success
	bufferxfer_exec_f, //Buffer transfer execute fail
	ind_transp_f,  //indication transport fail  timeout and peer close
	p_ind_change_state, // indication change state
	p_ind_timeout,		// indication timeout
	p_invalid
};

enum t_ack_status
{
	t_ack_none,
	t_ack_s,
	t_nack_s,
	t_buffxfer_cont
};

enum com_module{m_isr,ul,ul_rt,m_wireless,m_self};

#ifdef ST
typedef enum _Wireless_trigger
{
	w_invalid                   ,
	w_enable_ble	 			,
	w_disable_ble 				,
	w_enable_wifi  				,
	w_disable_wifi 				,
	w_disable_wireless			,
	w_ble_boot_success			,
	w_ble_boot_failure			,
	w_wifi_boot_success			,
	w_wifi_boot_failure			,
	w_wifi_scan_initiate		,
}w_trigger_t;
#endif

typedef struct primitive_trigger
{
	enum comm_subid sub_id;
	enum _ptrigger trigger;
	void *payload;
	uint16_t len;
	uint8_t retry;
	com_callback function;

}pe_trigger;

typedef struct transport_trigger
{
	enum _ttrigger  trigger;
}tt_trigger;

typedef struct Comm_Queue
{
	enum com_module module;
	union
	{
		pe_trigger pt;
		tt_trigger tt;
#ifdef ST
		w_trigger_t wt;
#endif
	}event;

}Comm_Queue_t;

uint32_t RunMainLoop(Comm_Queue_t msg);

uint32_t startCRTimer(uint32_t period);
uint32_t stopCRTimer();
uint32_t startITimer(uint32_t period);
uint32_t stopITimer();

int32_t resetPrimitiveMaster(enum _Exchange_Type primitive);
int32_t resetPrimitiveManager(void);
int32_t timeoutPrimitiveManager(void);
int32_t resetTransportManager(void);

struct Buffer_Xfer* GetBufferXferContext();
struct _Exchange* GetTManagerContext();
int GetTransportManagerState(void);
int IsTransportMaster();
int isComClose(void);
uint32_t IsComMessageEmpty();
void triggerFromPrimitive(pe_trigger* retrans);
int32_t triggerCommMngr(Comm_Queue_t* msg);
void SetTransportTrigger(enum _ttrigger _trigger);
void GeneratePrimitiveTrigger(enum comm_subid c_subid);
void SetPrimitiveTrigger_Data(enum _ptrigger _trigger,uint8_t* data,uint16_t len,enum comm_subid subid);
void SetPrimitiveTrigger_UL(enum _ptrigger _trigger,uint8_t* data, uint16_t len, enum comm_subid subid, uint8_t retry, com_callback callback);
void SetPrimitiveTrigger_RT(enum _ptrigger _trigger,uint8_t* data, uint16_t len, enum comm_subid subid);
uint32_t GetPrimitiveTrigger(pe_trigger* trigger);
enum _ttrigger GetTransportTrigger();
uint32_t TransportMain(enum _ttrigger _trigger);
uint32_t PrimitiveMain(pe_trigger* trigger);
int32_t IsCommand(enum comm_subid c_subid);
void WriteCmd (struct _Exchange* pcommand,enum comm_subid c_subid);
int32_t UpdatePrimitiveTriggerRetry(pe_trigger* trigger);

uint16_t getMtuSize(enum comm_subid subid);

void Comm_Can_Sleep();
void Comm_Sleep_Set();
void Comm_Sleep_Clear();
void Comm_Sleep_Clear_isr();
void Handle_Sleep(void);

int32_t isCommConnected(void);
int32_t isCommDisconnected(void);
int32_t setCommState(int32_t _state);

#if SEMP_TEST
void TestComplete(void);
#endif

#endif /* _COMM_MANAGER_H_ */
