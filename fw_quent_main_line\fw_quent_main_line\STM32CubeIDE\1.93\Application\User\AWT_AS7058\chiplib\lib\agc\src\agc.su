agc.c:233:13:update_circular_buff	16	static
agc.c:254:16:calc_moving_mean	24	static
agc.c:276:16:calc_exp_mov_mean_filter	24	static
agc.c:296:17:calc_mov_std_deviation	48	static
agc.c:316:16:calc_gradient	24	static
agc.c:339:16:mov_median_filter	48	static
agc.c:370:13:filter_reset	16	static
agc.c:381:13:lp_filter_reset	16	static
agc.c:392:13:reset_amplitude_estimation	16	static
agc.c:408:13:calc_signal_params	16	static
agc.c:435:13:calc_step_detection_params	16	static
agc.c:452:13:calc_pd_offset_signal_step_size	32	static
agc.c:478:13:pd_offset_step_found	24	static
agc.c:499:13:detect_pd_offset_steps	24	static
agc.c:527:19:pd_offset_control	48	static
agc.c:648:13:estimate_amplitude	16	static
agc.c:682:19:led_current_control	24	static
agc.c:782:12:agc_initialize	8	static
agc.c:793:12:agc_set_configuration	24	static
agc.c:870:12:agc_get_configuration	16	static
agc.c:889:12:agc_start_processing	32	static
agc.c:1008:12:agc_execute	448	static
agc.c:1110:12:agc_get_status	16	static
agc.c:1129:12:agc_stop_processing	16	static
agc.c:1145:12:agc_shutdown	8	static
agc.c:1152:12:agc_get_version	16	static
