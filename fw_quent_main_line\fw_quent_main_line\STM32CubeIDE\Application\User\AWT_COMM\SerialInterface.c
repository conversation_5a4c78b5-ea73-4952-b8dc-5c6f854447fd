/*
 * SerialInterface.c
 *
 *  Created on: 13-Sep-2023
 *      Author: admin
 */

#include "stm32l4xx_hal.h"
#include <string.h>
#include "ring_buf.h"
#include "systemconfig.h"
#include "Lib\Comm_Manager.h"
#include "SerialInterface.h"
#include "port193.h"
#include "event_handler.h"
#include "watch_state.h"
#include "Comm_Debug.h"

enum radioType cRadio = Radio_WiFi;
extern UART_HandleTypeDef huart3;
extern UART_HandleTypeDef huart5;
UART_HandleTypeDef* huartX []={Radio_None,&huart3, &huart5};

volatile uint32_t rx_flag = 0;
volatile uint32_t DMA_calls = 0;

volatile uint16_t DMA_RX_Count = 0;
volatile uint32_t DMA_Error = 0;
volatile uint16_t DMA_Req_Size = 0;

volatile uint16_t p_DMA_RX_Count = 0;
volatile uint32_t p_DMA_Error = 0;
volatile uint16_t p_DMA_Req_Size = 0;

struct PacketBuffer
{
	uint8_t  max;
	uint8_t  current;
	struct
	{
		uint8_t* buf;
		uint16_t len;
	}Segment[3];
};

static struct PacketBuffer dma_buf;

/********************************************************************************
 * get_crc
 * Generates and returns a 32-bit polynomial CRC.
 ********************************************************************************/
static uint32_t _get_crc(struct PacketBuffer pbuf)
{
	uint32_t crc = 0xFFFFFFFF;
	uint32_t b = 0;
	uint8_t ch = 0x00;

	for(;pbuf.current < pbuf.max; pbuf.current++)
	{
		for(uint32_t index = 0; index < pbuf.Segment[pbuf.current].len ; index++)
		{
			ch = pbuf.Segment[pbuf.current].buf[index];
			for(uint8_t j=0; j<8 ; j++)
			{
				b =(ch^crc)&1;
				crc>>=1;
				if(b)
					crc=crc^0xEDB88320;
				ch>>=1;
			}
		}
	}
	return ~crc ;
}

uint32_t get_crc(uint8_t *data, uint32_t len)
{
	uint32_t crc = 0xFFFFFFFF;
	uint32_t b = 0;
	uint8_t ch = 0x00;

		for(uint32_t index = 0; index < len ; index++)
		{
			ch = data[index];
			for(uint8_t j=0; j<8 ; j++)
			{
				b =(ch^crc)&1;
				crc>>=1;
				if(b)
					crc=crc^0xEDB88320;
				ch>>=1;
			}
		}
	return ~crc ;
}

typedef void (*wUartTx)(uint8_t *, uint16_t );

static void bleUartTx( uint8_t *pData, uint16_t Size);
static void wifiUartTx( uint8_t *pData, uint16_t Size);
static void dummyUartTx( uint8_t *pData, uint16_t Size);

static wUartTx wSend[] = {
		                    dummyUartTx,
		                    bleUartTx,
		                    wifiUartTx
                         };

static void wakeupble(uint8_t action)
{
	if(action == 1)
		HAL_GPIO_WritePin(ST_BLE_WKE_PORT, ST_BLE_WKE_PIN, SET);
	else
		HAL_GPIO_WritePin(ST_BLE_WKE_PORT, ST_BLE_WKE_PIN, RESET);
}

static void wifi_timeout_trig(void)
{
	HAL_GPIO_WritePin(RTC_WAKE_UP_PORT, RTC_WAKE_UP_PIN, RESET);
	HAL_Delay(10);
	HAL_GPIO_WritePin(RTC_WAKE_UP_PORT, RTC_WAKE_UP_PIN, SET);
}

static void wakeupwifi(uint8_t action)
{
	if(action == 1)
		HAL_GPIO_WritePin(ST_WIFI_WAKEUP_PORT, ST_WIFI_WAKEUP_PIN, SET);
	else
		HAL_GPIO_WritePin(ST_WIFI_WAKEUP_PORT, ST_WIFI_WAKEUP_PIN, RESET);
}

void wakeupWireless(uint8_t action)
{
	switch(cRadio)
	{
	case Radio_BLE:
		wakeupble(action);
		break;
	case Radio_WiFi:
		wakeupwifi(action);
		break;
	default:
		break;
	}
}

	static uint32_t blestatus()
{
	return HAL_GPIO_ReadPin(BLE_INT2_PORT, BLE_INT2_PIN);
}

static uint32_t wifistatus()
{
	return HAL_GPIO_ReadPin(WIFI_INT_PORT, WIFI_INT_PIN);
}

uint32_t WirelessStatus()
{
	switch(cRadio)
	{
	case Radio_BLE:
		return blestatus();
		break;
	case Radio_WiFi:
		return wifistatus();
		break;
	default:
		break;
	}
	return -1;
}
uint32_t WirelessPortStatus()
{
	switch(cRadio)
	{
	case Radio_BLE:
		return HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_11);
		break;
	case Radio_WiFi:
		return HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_12);
		break;
	default:
		break;
	}
	return 0;
}

static void dummyUartTx( uint8_t *pData, uint16_t Size)
{
	return;
}


/********************************************************************************
 * bleUartTx
 * Transmit the given data over UART DMA.
 ********************************************************************************/
static void bleUartTx( uint8_t *pData, uint16_t Size)
{
	HAL_UART_Transmit_DMA(&huart3, pData, Size);
}

/********************************************************************************
 * wifiUartTx
 * Transmit the given data over UART DMA.
 ********************************************************************************/
static void  wifiUartTx( uint8_t *pData, uint16_t Size)
{
	HAL_UART_Transmit_DMA(&huart5, pData, Size);
}

static void WirelessUartTx(void)
{
	if(dma_buf.current < dma_buf.max)
	{
		uint8_t curSeg = dma_buf.current++;
		(*wSend[cRadio])(dma_buf.Segment[curSeg].buf, dma_buf.Segment[curSeg].len);
	}
}

static void InitDMABuffer(uint8_t* buffer, uint16_t length)
{
	 dma_buf.current         = 0;
	 dma_buf.max             = 1;
	 dma_buf.Segment[0].buf  = buffer;
	 dma_buf.Segment[0].len  = length;
}

void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance == UART5 || huart->Instance == USART3)
	{
		if(huart->ErrorCode & HAL_UART_ERROR_FE)
		{
			COM_BreakEvent();
		}
	}
}
static uint8_t Open_Uart(UART_HandleTypeDef* huart)
{
	__HAL_UART_SEND_REQ(huart, UART_RXDATA_FLUSH_REQUEST);
	// initialise ring buffer
	RingBuf_Init(&(huart->rBuffer), sizeof(huart->rBuffer.buf));
	// initialise ring buffer end

	HAL_UARTEx_ReceiveToIdle_DMA(huart, &huart->rBuffer.buf[huart->rBuffer.head], RING_BUFF);
	return 1;
}

static void Close_Uart(UART_HandleTypeDef* huart)
{
	// Ringbuffer de initialise
	HAL_UART_Abort_IT(huart);
	RingBuf_DeInit(&(huart->rBuffer));
}

void Close_Rbuffer(void)
{
	switch(cRadio)
	{
	case Radio_BLE:
		Close_Uart(huartX[cRadio]);
		BLE_DMA_DeInit();
		break;
	case Radio_WiFi:
		Close_Uart(huartX[cRadio]);
		WiFi_DMA_DeInit();
		break;
	default:
		return;
	}
	HAL_UART_DeInit(huartX[cRadio]);
}

void Open_Rbuffer(void)
{
	switch(cRadio)
	{
	case Radio_BLE:
		BLE_DMA_Init();
		MX_USART3_UART_Init(460800,UART_MODE_TX_RX);  // Doubled from 230400
		break;
	case Radio_WiFi:
		WiFi_DMA_Init();
		MX_UART5_UART_Init(230400,UART_MODE_TX_RX);
		break;
	default:
		return;
	}
		Open_Uart(huartX[cRadio]);
}

int32_t rbuf_get(uint8_t *buffer, uint16_t len)
{
	if(cRadio != Radio_None)
		return RingBuf_get(&(huartX[cRadio]->rBuffer), buffer, len);

	return 0;
}

void Debug_DMA_Interrupt()
{
	printf("Debugging DMA interrupt called %ld \n",DMA_calls);
	printf("Size  %d  Prev Size  %d \n",DMA_Req_Size,p_DMA_Req_Size);
	printf("Error %ld Prev Error %ld \n",DMA_Error,p_DMA_Error);
	printf("Count %d  Prev Count %d \n",DMA_RX_Count,p_DMA_RX_Count);

}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	uint16_t wSize = 0;
	uint16_t offset = huart->RxDMABufOffset;
    //TODO handle the error
	huart->ErrorCode = 0x00;

	if(Size > 0)
	{
		wSize = RingBuf_put(&huart->rBuffer,&huart->RxDMABuf[offset], Size);
		if(wSize)
		{
			offset += Size;
			huart->RxDMABufOffset = offset<RxDMABufSize?offset:0;
		}
	}

	if(huart->rxEvent == RX_IDLE)
	{
		COM_RXEvent();
		huart->rxEvent = RX_EVENT_CLEAR;
	}
}

static void BleBootTxcplt(UART_HandleTypeDef *huart)
{
	BaseType_t xHigherPriorityTaskWoken;
	xHigherPriorityTaskWoken = pdFALSE;
	if(huart->hSem != NULL)
		xSemaphoreGiveFromISR(huart->hSem, &xHigherPriorityTaskWoken);
}

static void pendingDmaTx(UART_HandleTypeDef *huart)
{
	if(dma_buf.current < dma_buf.max)
	{
		uint8_t curSeg = dma_buf.current++;
		HAL_UART_Transmit_DMA(huart, dma_buf.Segment[curSeg].buf, dma_buf.Segment[curSeg].len);
	}
	else
	{
		COM_TXCEvent();
	}
}

void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance == USART3 && huart->Init.BaudRate == 115200)  // add for USART% also
	{
		BleBootTxcplt(huart);
	}
	else if(huart->Instance == USART3 || huart->Instance == UART5)
	{
		pendingDmaTx(huart);
	}
	else
	{
	}

}

void updateWirelessModule(enum radioType radio)
{
	switch(radio)
	{
	case Radio_None:
		cRadio = Radio_None;
		break;
	case Radio_BLE:
		cRadio = Radio_BLE;
		break;
	case Radio_WiFi:
		cRadio = Radio_WiFi;
		break;
	default:
		cRadio = Radio_None;
		break;
	}
}

uint16_t getMtuSize(enum comm_subid subid)
{
	int mtu = 0;
	if(cRadio == Radio_BLE)
	{
		switch(subid)
		{
		case comm_wECGSamples:
			mtu = 200;  // Increased from 150 to 200
			break;
		case comm_wUconfig:
			mtu = 128;
			break;
		case comm_wSpo2rawData:
		case comm_wEcgrawData:
			mtu = 240;  // Increased from 160 to 240 for raw data
			break;
		default:
			mtu = 128;
			break;
		}
	}
	else if(cRadio == Radio_WiFi)
	{
		mtu = 3000;
	}
	else
	{
		mtu = 0;
	}
	return mtu;
}

enum radioType getRadioType(void)
{
	return cRadio;
}

static uint32_t Radio_RST_State()
{
	switch(Get_Com_State())
	{
	case S_WIR_ON:
	case S_WIR_OFF:
	case S_BLE_ON:
		BootBLE();
		break;
	case S_WIFI_ON:
		WiFi_Init();
		break;
	default:
		break;
	}
	return 1;
}

static uint32_t Radio_PWRON_State()
{
	switch(Get_Com_State())
	{
	case S_WIR_ON:
	case S_WIR_OFF:
	case S_BLE_ON:
		BootBLE();
		break;
	case S_WIFI_ON:
		WiFi_Init();
		break;
	default:
		break;
	}

	return 1;
}

static uint32_t  Radio_Sleep_State(wakeup_trigger_t trigger)
{
	switch(Get_Com_State())
	{
	case S_WIR_ON:
	case S_WIR_OFF:
	case S_BLE_ON:
		BLE_Init();
		cRadio = Radio_BLE;
		setCommState(Get_Wireless_Status());
		break;
	case S_WIFI_ON:
		WiFi_Init();
		cRadio = Radio_WiFi;
		setCommState(Get_Wireless_Status());
		break;
	default:
		setCommState(wireless_off);
		break;
	}

	if(trigger == BLE_TRIGG)
	{
		COM_BLEWakeup(D_High);
		return 0;
	}
	else if(trigger == WIFI_TRIGG)
	{
		COM_WiFiWakeup(D_High);
		return 0;
	}
	else
		return 1;
}

static uint8_t InitializeRadio(enum awt_watch_state state,wakeup_trigger_t trigger)
{
	switch(state)
	{

	case HW_RST:
	case SW_RST:
		return Radio_RST_State();
		break;

	case PWR_ON:
		return Radio_PWRON_State();
		break;

	case SLEEP_WKE:
		return Radio_Sleep_State(trigger);

	default:
		break;

	}

	return 1;
}

uint32_t Init_Wireless(void* argument)
{
	InitializeRadio((enum awt_watch_state)((0x0000FF00 & ((uint32_t)argument)) >> 8),(wakeup_trigger_t)((uint32_t)argument) & 0xFF);
	return 1;
}

static uint8_t Start_RX(UART_HandleTypeDef* huart)
{
	__HAL_UART_SEND_REQ(huart, UART_RXDATA_FLUSH_REQUEST);
	RingBuf_Init(&(huart->rBuffer), sizeof(huart->rBuffer.buf));

	DMA_RX_Count = 0;
	DMA_Error = 0;
	DMA_Req_Size = 0;

    DMA_calls = 0;

	p_DMA_RX_Count = 0;
	p_DMA_Error = 0;
	p_DMA_Req_Size = 0;

	//huart->RxDMABufOffset = 0
	HAL_UARTEx_ReceiveToIdle_DMA(huart, huart->RxDMABuf, RxDMABufSize);
	return 1;
}

static void Stop_RX(UART_HandleTypeDef* huart)
{
	// Ringbuffer de initialise
	HAL_UART_Abort_IT(huart);

}

void Open_Master_tPort()
{
	switch(cRadio)
	{
	case Radio_BLE:
		BLE_DMA_Init();
		MX_USART3_UART_Init(460800,UART_MODE_TX);  // Doubled from 230400
		break;
	case Radio_WiFi:
		WiFi_DMA_Init();
		MX_UART5_UART_Init(230400,UART_MODE_TX);
		break;
	default:
		return;
	}

	wakeupWireless(1);
}

void Open_Master_rPort()
{
	if(cRadio != Radio_None)
	{
		huartX[cRadio]->Init.Mode = UART_MODE_TX_RX;
		UART_SetConfig(huartX[cRadio]);
		Start_RX(huartX[cRadio]);
	}
}

void Open_Slave_Port()
{
	if(cRadio != Radio_None)
	{
		switch(cRadio)
		{
		case Radio_BLE:
			BLE_DMA_Init();
			MX_USART3_UART_Init(230400,UART_MODE_TX_RX);
			break;
		case Radio_WiFi:
			WiFi_DMA_Init();
			MX_UART5_UART_Init(230400,UART_MODE_TX_RX);
			break;
		default:
			return;
		}

		Start_RX(huartX[cRadio]);
		wakeupWireless(1);
	}
}

void hClose_Port()
{
	wakeupWireless(0);
}

void Close_Port()
{
	if(cRadio != Radio_None)
	{

		wakeupWireless(0);
		Stop_RX(huartX[cRadio]);
		switch(cRadio)
		{
		case Radio_BLE:
			BLE_DMA_DeInit();
			break;
		case Radio_WiFi:
			WiFi_DMA_DeInit();
			break;
		default:
			return;
		}
		HAL_UART_DeInit(huartX[cRadio]);
		memset(huartX[cRadio], 0, sizeof(UART_HandleTypeDef));
	}
}

void Reset_Port()
{
	if(cRadio != Radio_None)
	{
		Stop_RX(huartX[cRadio]);
		switch(cRadio)
		{
		case Radio_BLE:
			BLE_DMA_DeInit();
			break;
		case Radio_WiFi:
		{
			wifi_timeout_trig();
			WiFi_DMA_DeInit();
		}
		break;
		default:
			return;
		}
		HAL_UART_DeInit(huartX[cRadio]);
		memset(huartX[cRadio], 0, sizeof(UART_HandleTypeDef));
	}
}

void Refresh_Port()
{
	COM_PRINTI("Refreshing Port \n");
	Debug_DMA_Interrupt();
	HAL_UART_DMAStop(huartX[cRadio]);
	Start_RX(huartX[cRadio]);
}

void sendBreakCmd(void)
{
	if(cRadio != Radio_None)
	{
		HAL_Delay(200);
		__HAL_UART_SEND_REQ(huartX[cRadio], UART_SENDBREAK_REQUEST);
	}
}


int32_t Com_BLE_ON(void)
{
	Comm_Queue_t msg;
	memset(&msg, 0, sizeof(msg));
	msg.module 			 = m_wireless;
	msg.event.wt  		 = w_enable_ble;
	triggerCommMngr(&msg);
	return 1;
}

int32_t Com_BLE_OFF(void)
{
	Comm_Queue_t msg;
	memset(&msg, 0, sizeof(msg));
	msg.module 			 = m_wireless;
	msg.event.wt  		 = w_disable_ble;
	triggerCommMngr(&msg);
	return 1;
}

int32_t Com_Wifi_ON(void)
{
	Comm_Queue_t msg;
	memset(&msg, 0, sizeof(msg));
	msg.module 			 = m_wireless;
	msg.event.wt  		 = w_enable_wifi;
	triggerCommMngr(&msg);
	return 1;
}

int32_t Com_Wifi_OFF(void)
{
	Comm_Queue_t msg;
	memset(&msg, 0, sizeof(msg));
	msg.module 			 = m_wireless;
	msg.event.wt  		 = w_disable_wifi;
	triggerCommMngr(&msg);
	return 1;
}

int32_t Com_BLE_Boot_Success(void)
{
	Comm_Queue_t msg;
	memset(&msg, 0, sizeof(msg));
	msg.module 			 = m_wireless;
	msg.event.wt  		 = w_ble_boot_success;
	triggerCommMngr(&msg);
	return 0;
}

void COM_BLEWakeup (enum Signal signal)
{
	Comm_Queue_t msg;
	msg.module		 	    = m_isr;
	msg.event.tt.trigger    = (signal == D_High) ? st_wakeup:peer_close;

	if(signal == D_High)
	{
		Comm_Sleep_Clear_isr();
	}
	else if (signal == D_Low)
	{
		Comm_Can_Sleep();
	}

	triggerCommMngr(&msg);
}

void COM_WiFiWakeup(enum Signal signal)
{
	Comm_Queue_t msg;
	msg.module 			    = m_isr;
	msg.event.tt.trigger 	= (signal == D_High) ? st_wakeup:peer_close;

	if(signal == D_High)
	{
		Comm_Sleep_Clear_isr();
	}
	else if (signal == D_Low)
	{
		Comm_Can_Sleep();
	}

	triggerCommMngr(&msg);
}

int _platform_transmit(uint8_t *data, uint16_t len)
{
	InitDMABuffer(data,len);
	WirelessUartTx();
    return 0;
}

int _platform_break_handle(void)
{
	if(WirelessStatus()) // open state
	{
		if(WirelessPortStatus())  // break sent by peer
		{
			return STATE_WAIT;
		}
		else   // port closed
		{
			if(IsTransportMaster() || timeoutPrimitiveManager()) // slave received packets with error and reset the port
			{

				return STATE_CHANGE;

			}

		}
	}
	else
	{ // TODO ?? close state

	}

	return -1;
}


#ifdef IF_S_TX_TIMING
void ADJUST_SLAVE_TIMING()
{
	HAL_Delay(S_TX_DELAY);
}

#endif
