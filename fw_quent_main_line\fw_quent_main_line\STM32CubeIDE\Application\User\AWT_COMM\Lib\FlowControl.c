#include "FlowControl.h"
#include <stdio.h>
#include <string.h>

// Global flow control instance
static flow_control_t g_flow_ctrl = {0};

void flow_control_init(void) {
    memset(&g_flow_ctrl, 0, sizeof(flow_control_t));
    g_flow_ctrl.optimal_delay = DEFAULT_DELAY_MS;
    g_flow_ctrl.current_state = FLOW_STATE_NORMAL;
    g_flow_ctrl.min_ack_time = UINT32_MAX;
}

void flow_control_reset(void) {
    g_flow_ctrl.consecutive_timeouts = 0;
    g_flow_ctrl.consecutive_fast_acks = 0;
    g_flow_ctrl.packets_sent = 0;
    g_flow_ctrl.packets_acked = 0;
    g_flow_ctrl.ack_index = 0;
    memset(g_flow_ctrl.ack_times, 0, sizeof(g_flow_ctrl.ack_times));
}

uint16_t flow_control_get_delay(uint32_t data_size) {
    transfer_mode_t mode = flow_control_get_transfer_mode(data_size);
    
    switch (mode) {
        case TRANSFER_MODE_NORMAL:
            return g_flow_ctrl.optimal_delay;
            
        case TRANSFER_MODE_BURST:
            // Reduce delay for burst mode
            return g_flow_ctrl.optimal_delay / 2;
            
        case TRANSFER_MODE_STREAMING:
            // Minimal delay for streaming
            return MIN_DELAY_MS;
            
        default:
            return DEFAULT_DELAY_MS;
    }
}

void flow_control_update_ack_time(uint32_t ack_time) {
    g_flow_ctrl.packets_acked++;
    
    // Update statistics
    g_flow_ctrl.last_ack_time = ack_time;
    g_flow_ctrl.ack_times[g_flow_ctrl.ack_index] = ack_time;
    g_flow_ctrl.ack_index = (g_flow_ctrl.ack_index + 1) % FLOW_CONTROL_WINDOW;
    
    // Calculate average ACK time
    uint32_t total = 0;
    uint8_t count = 0;
    for (int i = 0; i < FLOW_CONTROL_WINDOW; i++) {
        if (g_flow_ctrl.ack_times[i] > 0) {
            total += g_flow_ctrl.ack_times[i];
            count++;
        }
    }
    
    if (count > 0) {
        g_flow_ctrl.avg_ack_time = total / count;
    }
    
    // Update min/max
    if (ack_time < g_flow_ctrl.min_ack_time) {
        g_flow_ctrl.min_ack_time = ack_time;
    }
    if (ack_time > g_flow_ctrl.max_ack_time) {
        g_flow_ctrl.max_ack_time = ack_time;
    }
    
    // Adaptive delay adjustment
    if (ack_time < 50) {  // Fast ACK
        g_flow_ctrl.consecutive_fast_acks++;
        g_flow_ctrl.consecutive_timeouts = 0;
        
        if (g_flow_ctrl.consecutive_fast_acks >= 5) {
            // Decrease delay for better throughput
            if (g_flow_ctrl.optimal_delay > MIN_DELAY_MS) {
                g_flow_ctrl.optimal_delay--;
            }
            g_flow_ctrl.consecutive_fast_acks = 0;
            g_flow_ctrl.current_state = FLOW_STATE_OPTIMIZED;
        }
    } else if (ack_time > 500) {  // Slow ACK
        g_flow_ctrl.consecutive_fast_acks = 0;
        // Increase delay to reduce congestion
        if (g_flow_ctrl.optimal_delay < MAX_DELAY_MS) {
            g_flow_ctrl.optimal_delay += 2;
        }
        g_flow_ctrl.current_state = FLOW_STATE_CONGESTED;
    } else {
        g_flow_ctrl.current_state = FLOW_STATE_NORMAL;
    }
}

void flow_control_timeout_occurred(void) {
    g_flow_ctrl.consecutive_timeouts++;
    g_flow_ctrl.consecutive_fast_acks = 0;
    
    if (g_flow_ctrl.consecutive_timeouts >= MAX_CONSECUTIVE_TIMEOUTS) {
        // Significant increase in delay
        g_flow_ctrl.optimal_delay = (g_flow_ctrl.optimal_delay * 3) / 2;
        if (g_flow_ctrl.optimal_delay > MAX_DELAY_MS) {
            g_flow_ctrl.optimal_delay = MAX_DELAY_MS;
        }
        g_flow_ctrl.current_state = FLOW_STATE_TIMEOUT;
    }
}

transfer_mode_t flow_control_get_transfer_mode(uint32_t data_size) {
    if (data_size < 1000) {
        return TRANSFER_MODE_NORMAL;
    } else if (data_size < 10000) {
        return TRANSFER_MODE_BURST;
    } else {
        return TRANSFER_MODE_STREAMING;
    }
}

void flow_control_optimize_for_mode(transfer_mode_t mode) {
    switch (mode) {
        case TRANSFER_MODE_NORMAL:
            g_flow_ctrl.optimal_delay = DEFAULT_DELAY_MS;
            break;
            
        case TRANSFER_MODE_BURST:
            g_flow_ctrl.optimal_delay = DEFAULT_DELAY_MS / 2;
            break;
            
        case TRANSFER_MODE_STREAMING:
            g_flow_ctrl.optimal_delay = MIN_DELAY_MS;
            break;
    }
}

void flow_control_print_stats(void) {
    printf("Flow Control Statistics:\n");
    printf("  Packets Sent: %d\n", g_flow_ctrl.packets_sent);
    printf("  Packets ACKed: %d\n", g_flow_ctrl.packets_acked);
    printf("  Average ACK Time: %d ms\n", g_flow_ctrl.avg_ack_time);
    printf("  Min ACK Time: %d ms\n", g_flow_ctrl.min_ack_time);
    printf("  Max ACK Time: %d ms\n", g_flow_ctrl.max_ack_time);
    printf("  Optimal Delay: %d ms\n", g_flow_ctrl.optimal_delay);
    printf("  Consecutive Timeouts: %d\n", g_flow_ctrl.consecutive_timeouts);
    printf("  Current State: %d\n", g_flow_ctrl.current_state);
    
    if (g_flow_ctrl.packets_sent > 0) {
        uint32_t success_rate = (g_flow_ctrl.packets_acked * 100) / g_flow_ctrl.packets_sent;
        printf("  Success Rate: %d%%\n", success_rate);
    }
}
