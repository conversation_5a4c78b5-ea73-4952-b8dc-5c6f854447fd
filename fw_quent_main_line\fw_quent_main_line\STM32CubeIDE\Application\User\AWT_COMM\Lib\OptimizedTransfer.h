#ifndef OPTIMIZED_TRANSFER_H
#define OPTIMIZED_TRANSFER_H

#include <stdint.h>
#include "stm32l4xx_hal.h"
#include "Wireless.h"

// Function prototypes for optimized transfer
int32_t Com_BufferXfer_Optimized(enum comm_subid subid, uint8_t* data, uint16_t len, com_callback callback);
void optimized_transfer_packet_sent(uint8_t segment_num, uint16_t total_segments);
void optimized_transfer_packet_acked(uint32_t ack_time);
void optimized_transfer_packet_failed(void);
void optimized_transfer_retransmission(void);
uint32_t optimized_transfer_get_recommended_delay(uint32_t data_size);
void optimized_transfer_print_statistics(void);
void test_optimized_throughput(void);

// Macros to replace original function calls
#define Com_BufferXfer_Fast(subid, data, len, callback) \
    Com_BufferXfer_Optimized(subid, data, len, callback)

#endif // OPTIMIZED_TRANSFER_H
