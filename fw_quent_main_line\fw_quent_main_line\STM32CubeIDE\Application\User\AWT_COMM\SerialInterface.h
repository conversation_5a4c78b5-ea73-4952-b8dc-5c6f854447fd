/*
 * SerialInterface.h
 *
 *  Created on: 16-Oct-2023
 *      Author: admin
 */

#ifndef APPLICATION_USER_AWT_WIRELESS_OPERATIONS_SERIALINTERFACE_H_
#define APPLICATION_USER_AWT_WIRELESS_OPERATIONS_SERIALINTERFACE_H_

//#define IF_S_TX_TIMING
#define S_TX_DELAY 0  // Removed 2ms delay for better throughput
#define S_TX_DELAY_LARGE_TRANSFER 0  // No delay for large transfers


#ifdef IF_S_TX_TIMING
void ADJUST_SLAVE_TIMING();
#endif


#define DOUBLE_BUFFER
#define MAX_DATA_SMALL         1024 * 3     // 3KB for normal operations
#define MAX_DATA_LARGE         1024 * 8     // 8KB for large transfers
#define MAX_DATA               MAX_DATA_LARGE
#define LARGE_TRANSFER_THRESHOLD 5000       // 5KB threshold

enum radioType {Radio_None, Radio_BLE, Radio_WiFi};

void updateWirelessModule(enum radioType radio);
enum radioType getRadioType(void);
uint32_t Init_Wireless(void* argument);

int32_t Com_BLE_Boot_Success(void);

void COM_BLEWakeup 	(enum Signal signal);
void COM_WiFiWakeup	(enum Signal signal);

void switchOnBle(void);
void switchOffBle(void);
void BootBLE(void);
void BLE_Init(void);

void switchOnWifi(void);
void switchOffWifi(void);
void WiFi_Init(void);

void Open_Master_tPort();
void Open_Master_rPort();
void Open_Slave_Port();
void hClose_Port();
void Close_Port();
void Reset_Port();
void Refresh_Port();
void wakeupWireless(uint8_t action);
uint32_t WirelessStatus();
uint32_t WirelessPortStatus();
void sendBreakCmd(void);
int _platform_transmit(uint8_t *data, uint16_t len);
int _platform_break_handle(void);
void Open_Rbuffer(void);
void Close_Rbuffer(void);
int32_t rbuf_get(uint8_t *buffer, uint16_t len);
uint32_t get_crc(uint8_t *data, uint32_t len);

void Debug_DMA_Interrupt();

#endif /* APPLICATION_USER_AWT_WIRELESS_OPERATIONS_SERIALINTERFACE_H_ */
