Application/User/AWT_COMM/Lib/TransportQueue.o: \
 ../Application/User/AWT_COMM/Lib/TransportQueue.c \
 ../../Core/Inc/Awt_types.h \
 ../Application/User/AWT_COMM/Lib/TransportQueue.h \
 ../Application/User/AWT_COMM/Lib/Comm_Manager.h \
 ../../Core/Inc/Wireless.h ../../Core/Inc/database.h \
 ../../Core/Inc/Awt_types.h ../../Core/Inc/watch_state.h \
 ../../Core/Inc/AWT_UI_Wireless.h \
 D:/AS7050\ to\ As7058/fw_quent_main_line/fw_quent_main_line/STM32CubeIDE/Application/User/AWT_COMM/SerialInterface.h \
 D:/AS7050\ to\ As7058/fw_quent_main_line/fw_quent_main_line/STM32CubeIDE/Application/User/AWT_COMM/Comm_Debug.h

../../Core/Inc/Awt_types.h:

../Application/User/AWT_COMM/Lib/TransportQueue.h:

../Application/User/AWT_COMM/Lib/Comm_Manager.h:

../../Core/Inc/Wireless.h:

../../Core/Inc/database.h:

../../Core/Inc/Awt_types.h:

../../Core/Inc/watch_state.h:

../../Core/Inc/AWT_UI_Wireless.h:

D:/AS7050\ to\ As7058/fw_quent_main_line/fw_quent_main_line/STM32CubeIDE/Application/User/AWT_COMM/SerialInterface.h:

D:/AS7050\ to\ As7058/fw_quent_main_line/fw_quent_main_line/STM32CubeIDE/Application/User/AWT_COMM/Comm_Debug.h:
